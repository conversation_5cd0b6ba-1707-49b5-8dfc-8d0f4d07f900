<template>
  <div class="main-box" id="651513137705029">
    <!-- 左侧树状结构 -->
    <TreeFilter
      :default-expand-all="false"
      ref="treeFilter"
      label="name"
      :title="t('deviceProtect.maybeLife.organizationList')"
      :request-api="deviceProtectdailyProtectApi.getProdEquipmentMaterialInfoGetTree"
      @change1="handleNodeClick"
    >
      <template #shit>
        <CalendarCustom current-theme="dark" :header-title="title" :date-data="dateData"> </CalendarCustom>
      </template>
    </TreeFilter>
    <div class="table-box">
      <!-- 右侧表格 -->
      <ProTable
        :init-param="initParam"
        ref="proTableRef"
        :columns="columns"
        :request-api="fetchFilteredData"
        @row-click="handleRowClick"
        @selection-change="handleSelectionChange"
      >
        <!-- 操作按钮 -->
        <template #tableHeader>
          <div class="button-group">
            <el-button type="primary" @click="openAddDialog">{{ t("deviceProtect.maybeLife.add") }}</el-button>
            <el-button type="success" @click="importData">{{ t("deviceProtect.import") }}</el-button>
            <el-button type="primary" @click="exportData">{{ t("deviceProtect.maybeLife.export") }}</el-button>
            <el-button type="danger" :disabled="!selectedRows.length" @click="deleteSelectedRows">{{
              t("deviceProtect.maybeLife.batchDelete")
            }}</el-button>
          </div>
        </template>
        <!-- 操作列 -->
        <template #operation="{ row }">
          <div class="operation-buttons">
            <el-button type="primary" size="small" @click="openEditDialog(row)">{{ t("deviceProtect.maybeLife.edit") }}</el-button>
            <el-button type="danger" size="small" @click="deleteRow(row)">{{ t("deviceProtect.maybeLife.delete") }}</el-button>
            <el-button type="info" size="small" @click="openViewDialog(row)">{{ t("deviceProtect.maybeLife.view") }}</el-button>
          </div>
        </template>
      </ProTable>

      <!-- 新增/编辑/查看对话框 -->
      <el-dialog
        :width="1200"
        v-model="dialogVisible"
        :title="
          dialogType === 'add'
            ? t('deviceProtect.maybeLife.add')
            : dialogType === 'edit'
              ? t('deviceProtect.maybeLife.edit')
              : t('deviceProtect.maybeLife.view')
        "
      >
        <el-form :model="formData" ref="formRef" label-width="180px" :rules="rules" class="form-dialog">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item :label="t('deviceProtect.maybeLife.form.number')">
                <el-input v-model="formData.number" :disabled="dialogType === 'view'"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="t('deviceProtect.maybeLife.form.factory')">
                <el-input v-model="formData.factory" :disabled="dialogType === 'view'"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="t('deviceProtect.maybeLife.form.workshop')">
                <el-input v-model="formData.workshop" :disabled="dialogType === 'view'"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item :label="t('deviceProtect.maybeLife.form.productionLine')">
                <el-input v-model="formData.productionLine" :disabled="dialogType === 'view'"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="t('deviceProtect.maybeLife.form.machine')">
                <el-select v-model="formData.machine" :placeholder="t('deviceProtect.maybeLife.form.machine')" :disabled="dialogType === 'view'">
                  <el-option v-for="option in getMachineOptions" :key="option.value" :label="option.label" :value="option.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="t('deviceProtect.maybeLife.form.workstation')">
                <el-input v-model="formData.workstation" :disabled="dialogType === 'view'"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item :label="t('deviceProtect.maybeLife.form.component')">
                <el-select v-model="formData.component" :placeholder="t('deviceProtect.maybeLife.form.component')" :disabled="dialogType === 'view'">
                  <el-option v-for="option in componentOptions" :key="option.value" :label="option.label" :value="option.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="t('deviceProtect.maybeLife.form.workstationCategory')">
                <el-input v-model="formData.workstationCategory" :disabled="dialogType === 'view'"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="t('deviceProtect.maybeLife.form.materialCode')">
                <el-input v-model="formData.materialCode" :disabled="dialogType === 'view'"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item :label="t('deviceProtect.maybeLife.form.materialName')">
                <el-input v-model="formData.materialName" :disabled="dialogType === 'view'"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="t('deviceProtect.maybeLife.form.materialSpecification')">
                <el-input v-model="formData.materialSpecification" :disabled="dialogType === 'view'"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="t('deviceProtect.maybeLife.form.brand')">
                <el-input v-model="formData.brand" :disabled="dialogType === 'view'"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item :label="t('deviceProtect.maybeLife.form.theoreticalLife')" prop="theoreticalLife">
                <el-input-number v-model="formData.theoreticalLife" :disabled="dialogType === 'view'"></el-input-number>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="t('deviceProtect.maybeLife.form.experienceLife')" prop="experienceLife">
                <el-input-number v-model="formData.experienceLife" :disabled="dialogType === 'view'"></el-input-number>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="t('deviceProtect.maybeLife.form.usedLife')" prop="usedLife">
                <el-input-number v-model="formData.usedLife" :disabled="dialogType === 'view'"></el-input-number>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item :label="t('deviceProtect.maybeLife.form.lifeUnit')">
                <el-input v-model="formData.lifeUnit" :disabled="dialogType === 'view'"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="t('deviceProtect.maybeLife.form.lifeWarningLowerLimit')" prop="lifeWarningLowerLimit">
                <el-input-number v-model="formData.lifeWarningLowerLimit" :disabled="dialogType === 'view'"></el-input-number>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="t('deviceProtect.maybeLife.form.lifeWarningUpperLimit')" prop="lifeWarningUpperLimit">
                <el-input-number v-model="formData.lifeWarningUpperLimit" :disabled="dialogType === 'view'"></el-input-number>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item :label="t('deviceProtect.maybeLife.form.warningTime')" prop="warningTime">
                <el-date-picker
                  v-model="formData.warningTime"
                  type="datetime"
                  :placeholder="t('deviceProtect.maybeLife.form.warningTime')"
                  :disabled="dialogType === 'view'"
                ></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="t('deviceProtect.maybeLife.form.processingStatus')">
                <el-select
                  v-model="formData.processingStatus"
                  :placeholder="t('deviceProtect.maybeLife.form.processingStatus')"
                  :disabled="dialogType === 'view'"
                >
                  <el-option v-for="option in processingStatusOptions" :key="option.value" :label="option.label" :value="option.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item :label="t('deviceProtect.maybeLife.form.processor')">
                <el-input v-model="formData.processor" :disabled="dialogType === 'view'"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="t('deviceProtect.maybeLife.form.processingTime')">
                <el-date-picker
                  v-model="formData.processingTime"
                  type="datetime"
                  :placeholder="t('deviceProtect.maybeLife.form.processingTime')"
                  :disabled="dialogType === 'view'"
                ></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="t('deviceProtect.maybeLife.form.processingDuration')">
                <el-input-number v-model="formData.processingDuration" :disabled="dialogType === 'view'"></el-input-number>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item :label="t('deviceProtect.maybeLife.form.remarks')">
                <el-input v-model="formData.remarks" :disabled="dialogType === 'view'"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="dialogVisible = false">{{ t("deviceProtect.maybeLife.close") }}</el-button>
            <el-button v-if="dialogType !== 'view'" type="primary" @click="saveData">{{ t("deviceProtect.maybeLife.save") }}</el-button>
          </div>
        </template>
      </el-dialog>

      <!-- 导入文件选择框 -->
      <el-dialog :width="1200" v-model="importDialogVisible" :title="t('deviceProtect.maybeLife.import.title')">
        <el-upload ref="uploadRef" action="/api/sys/upload/uploadExcel" :auto-upload="false" :before-upload="beforeUpload">
          <el-button type="primary">{{ t("deviceProtect.maybeLife.import.selectFile") }}</el-button>
        </el-upload>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="importDialogVisible = false">{{ t("deviceProtect.maybeLife.import.cancel") }}</el-button>
            <el-button type="primary" @click="startImport">{{ t("deviceProtect.maybeLife.import.startImport") }}</el-button>
          </div>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ElMessage } from "element-plus"; // 引入 ElMessage
import { deviceProtectMaybeLifeApi, deviceProtectdailyProtectApi, productionReportCapacityApi } from "@/api";
import { convertCalendarData } from "@/utils";
import * as XLSX from "xlsx";
import { alramAnalysisApi } from "@/api";
import { useI18n } from "vue-i18n";
import { treeArr } from "@/config";
// 添加 onMounted 钩子，页面加载时自动触发搜索
onMounted(async () => {
  await fetchMachines(); // 挂载时获取机台
});
// 动态获取选项
const machineList = ref<any[]>([]); // 新增机台列表响应式变量
const getMachineOptions = computed(() => [
  // { label: "全部机台", value: "" }, // 添加空选项
  ...machineList.value.map(item => ({
    label: item.name,
    value: item.id
  }))
]);
const initParam = ref({
  // time: [moment().startOf("day").format("YYYY-MM-DD"), moment().endOf("day").format("YYYY-MM-DD")]
});
const { t } = useI18n();

const title = t("deviceProtect.maybeLife.title");
const dateData = ref([]);
const rules = {
  processingTime: [{ required: true, message: t("deviceProtect.maybeLife.validation.processingTime"), trigger: "blur" }],
  theoreticalLife: [{ required: true, message: t("deviceProtect.maybeLife.validation.theoreticalLife"), trigger: "blur" }],
  experienceLife: [{ required: true, message: t("deviceProtect.maybeLife.validation.experienceLife"), trigger: "blur" }],
  usedLife: [{ required: true, message: t("deviceProtect.maybeLife.validation.usedLife"), trigger: "blur" }],
  lifeWarningLowerLimit: [{ required: true, message: t("deviceProtect.maybeLife.validation.lifeWarningLowerLimit"), trigger: "blur" }],
  lifeWarningUpperLimit: [{ required: true, message: t("deviceProtect.maybeLife.validation.lifeWarningUpperLimit"), trigger: "blur" }],
  warningTime: [{ required: true, message: t("deviceProtect.maybeLife.validation.warningTime"), trigger: "blur" }]
};
const exportParam = ref({});

// ProTable 实例引用
const proTableRef = ref(null);
// 对话框相关
const dialogVisible = ref(false);
const importDialogVisible = ref(false);
const dialogType = ref("add"); // 对话框类型：add, edit, view
// 表单数据
const formData = reactive({
  number: "",
  factory: "",
  workshop: "",
  workline: "",
  deck: "",
  station: "",
  component: 0,
  workstationCategory: "",
  materialCode: "",
  materialName: "",
  materialSpecification: "",
  brand: "",
  theoreticalLife: 0,
  experienceLife: 0,
  usedLife: 0,
  lifeUnit: "",
  lifeWarningLowerLimit: 0,
  lifeWarningUpperLimit: 0,
  warningTime: "",
  warningStatus: "",
  processingStatus: "",
  processor: "",
  processingTime: "",
  processingDuration: 0,
  remarks: ""
});
// 表单引用
const formRef = ref(null);
// 上传组件引用
const uploadRef = ref(null);
// 选中的行数据
const selectedRows = ref([]);

// 组件选项
const componentOptions = [
  { value: "组件1", label: "组件1" },
  { value: "组件2", label: "组件2" },
  { value: "组件3", label: "组件3" },
  { value: "组件4", label: "组件4" },
  { value: "组件5", label: "组件5" }
];
// 处理状态选项
const processingStatusOptions = [
  { value: "unprocessed", label: t("deviceProtect.maybeLife.status.unprocessed") },
  { value: "processing", label: t("deviceProtect.maybeLife.status.processing") },
  { value: "processed", label: t("deviceProtect.maybeLife.status.processed") }
];
// 预警状态选项
const warningStatusOptions = [
  { value: "normal", label: t("deviceProtect.maybeLife.status.normal") },
  { value: "warning", label: t("deviceProtect.maybeLife.status.warning") },
  { value: "warningInProgress", label: t("deviceProtect.maybeLife.status.warningInProgress") },
  { value: "danger", label: t("deviceProtect.maybeLife.status.danger") }
];

// 表格列配置
const columns = computed(() => [
  { type: "selection", fixed: "left", width: 50 },
  { prop: "id", label: "Id", isShow: false },
  {
    prop: "number",
    label: t("deviceProtect.maybeLife.form.number"),
    width: 80,
    align: "center"
  },
  {
    prop: "factory",
    label: t("deviceProtect.maybeLife.form.factory"),
    width: 150,
    align: "left",
    search: { el: "input" }
  },
  {
    prop: "workshop",
    label: t("deviceProtect.maybeLife.form.workshop"),
    width: 150,
    align: "left",
    search: { el: "input" }
  },
  {
    prop: "workline",
    label: t("deviceProtect.maybeLife.form.workline"),
    width: 150,
    align: "left",
    search: { el: "input" }
  },
  {
    prop: "deck",
    label: t("deviceProtect.maybeLife.form.deck"),
    search: {
      el: "select",
      props: {
        placeholder: t("deviceProtect.maybeLife.form.deck"),
        clearable: true,
        options: getMachineOptions.value
      }
    }
  },
  {
    prop: "station",
    label: t("deviceProtect.maybeLife.form.station"),
    width: 150,
    align: "left",
    search: { el: "input" }
  },
  {
    prop: "component",
    label: t("deviceProtect.maybeLife.form.component"),
    enum: componentOptions,
    width: 120
  },
  {
    prop: "workstationCategory",
    label: t("deviceProtect.maybeLife.form.workstationCategory"),
    width: 150,
    align: "left"
  },
  {
    prop: "materialCode",
    label: t("deviceProtect.maybeLife.form.materialCode"),
    width: 150,
    align: "left"
  },
  {
    prop: "materialName",
    label: t("deviceProtect.maybeLife.form.materialName"),
    width: 150,
    align: "left"
  },
  {
    prop: "materialSpecification",
    label: t("deviceProtect.maybeLife.form.materialSpecification"),
    width: 150,
    align: "left"
  },
  {
    prop: "brand",
    label: t("deviceProtect.maybeLife.form.brand"),
    width: 100,
    align: "left"
  },
  {
    prop: "theoreticalLife",
    label: t("deviceProtect.maybeLife.form.theoreticalLife"),
    width: 100,
    align: "right"
  },
  {
    prop: "experienceLife",
    label: t("deviceProtect.maybeLife.form.experienceLife"),
    width: 100,
    align: "right"
  },
  {
    prop: "usedLife",
    label: t("deviceProtect.maybeLife.form.usedLife"),
    width: 100,
    align: "right"
  },
  {
    prop: "lifeUnit",
    label: t("deviceProtect.maybeLife.form.lifeUnit"),
    width: 180,
    align: "center"
  },
  {
    prop: "lifeWarningLowerLimit",
    label: t("deviceProtect.maybeLife.form.lifeWarningLowerLimit"),
    width: 100,
    align: "right"
  },
  {
    prop: "lifeWarningUpperLimit",
    label: t("deviceProtect.maybeLife.form.lifeWarningUpperLimit"),
    width: 100,
    align: "right"
  },
  {
    prop: "warningTime",
    label: t("deviceProtect.maybeLife.form.warningTime"),
    width: 180,
    align: "center"
  },
  {
    prop: "warningStatus",
    label: t("deviceProtect.maybeLife.form.warningStatus"),
    enum: warningStatusOptions,
    width: 100
  },
  {
    prop: "processingStatus",
    label: t("deviceProtect.maybeLife.form.processingStatus"),
    enum: processingStatusOptions,
    width: 100
  },
  {
    prop: "processor",
    label: t("deviceProtect.maybeLife.form.processor"),
    width: 150,
    align: "left"
  },
  {
    prop: "processingTime",
    label: t("deviceProtect.maybeLife.form.processingTime"),
    width: 180,
    align: "center"
  },
  {
    prop: "processingDuration",
    label: t("deviceProtect.maybeLife.form.processingDuration"),
    width: 100,
    align: "right"
  },
  {
    prop: "remarks",
    label: t("deviceProtect.maybeLife.form.remarks"),
    width: 200,
    align: "left"
  },
  { prop: "operation", label: t("deviceProtect.maybeLife.form.operation"), width: 230, fixed: "right" }
]);
// 1. 获取机台列表（Type=0）
const fetchMachines = async () => {
  try {
    console.log("开始获取机台列表");
    const response = await alramAnalysisApi.getListMesReportData({ Type: 0 });
    const list = response.data.list || [];

    machineList.value = list.map(item => ({
      id: item.MachineName || "未知机台",
      name: item.MachineName || "未知机台"
    }));
    // 添加：设置默认选中第一个机台
    // if (machineList.value.length > 0) {
    //   param.value.machine = machineList.value[0].id; // 设置默认机台 id
    // }

    console.log("机台列表已更新:", machineList.value);
  } catch (error) {
    // console.error("机台列表请求失败:", error);
    // ElMessage.error("获取机台列表失败");
  }
};

// 真实的数据请求
const fetchData = async param => {
  try {
    const response = await deviceProtectMaybeLifeApi.ProdEquipmentMaterialInfoGet(param);
    // 确保返回的数据格式正确
    if (response.code === 200 && Array.isArray(response.data.list)) {
      return {
        data: response.data
      };
    } else {
      console.error("数据格式不正确:", response);
      return {
        data: {
          total: 0,
          list: []
        }
      };
    }
  } catch (error) {
    console.error("获取数据失败:", error); // 添加错误日志输出
    ElMessage.error("获取数据失败");
    return {
      data: {
        total: 0,
        list: []
      }
    };
  }
};

// 过滤后的数据请求
// const currentFilter = ref(null);
const fetchFilteredData = async params => {
  const allData = await fetchData({ ...params, machine: params.deck || params.machine });
  exportParam.value.pageSize = allData.data.total;
  return allData;
};
const openAddDialog = () => {
  dialogType.value = "add";
  // 清空表单数据
  Object.keys(formData).forEach(key => {
    formData[key] = "";
  });
  dialogVisible.value = true;
};

// 打开编辑对话框
const openEditDialog = row => {
  dialogType.value = "edit";
  // 填充表单数据
  Object.assign(formData, row);
  dialogVisible.value = true;
};

// 打开查看详情对话框
const openViewDialog = row => {
  dialogType.value = "view";
  // 填充查看详情表单数据
  Object.assign(formData, row);
  dialogVisible.value = true;
};

// 保存数据
const saveData = async () => {
  try {
    const valid = await formRef.value.validate();
    if (valid) {
      let response;
      if (dialogType.value === "add") {
        response = await deviceProtectMaybeLifeApi.ProdEquipmentMaterialInfoAdd(formData);
      } else if (dialogType.value === "edit") {
        response = await deviceProtectMaybeLifeApi.ProdEquipmentMaterialInfoEdit(formData);
      }
      if (response && response.code === 200) {
        ElMessage.success(t("deviceProtect.maybeLife.save"));
        dialogVisible.value = false;
        proTableRef.value.getTableList();
        // 检查formData.component的值
        console.log("formData.component after save:", formData.component);
      } else {
        ElMessage.error(t("deviceProtect.maybeLife.validation.required"));
      }
    } else {
      ElMessage.error(t("deviceProtect.maybeLife.validation.required"));
    }
  } catch (error) {
    console.error(error);
  }
};
// const saveData = async () => {
//   if (!formData.factory) {
//     ElMessage.error("请填写必要信息");
//     return;
//   }
//   try {
//     let response;
//     if (dialogType.value === "add") {
//       response = await deviceProtectMaybeLifeApi.ProdEquipmentMaterialInfoAdd(formData);
//     } else if (dialogType.value === "edit") {
//       response = await deviceProtectMaybeLifeApi.ProdEquipmentMaterialInfoEdit(formData);
//     }
//     if (response && response.code === 200) {
//       ElMessage.success("保存成功");
//       dialogVisible.value = false;
//       // 刷新表格数据
//       proTableRef.value.getTableList();
//       // 重新构建树状结构数据
//     } else {
//       ElMessage.error("保存失败");
//     }
//   } catch (error) {
//     ElMessage.error("保存过程中出现错误");
//   }
// };
// // 删除行
// const deleteRow = async row => {
//   // 这里应该替换为实际的删除 API 请求
//   ElMessage.success(`删除 ${row.factory} 成功`);
//   // 刷新表格数据
//   proTableRef.value.getTableList();
//   // 重新构建树状结构数据
//   buildTreeData();
// };
// 删除行
const deleteRow = async row => {
  try {
    const response = await deviceProtectMaybeLifeApi.ProdEquipmentMaterialInfoDelete(row);

    if (response && response.code === 200) {
      ElMessage.success(t("deviceProtect.maybeLife.message.deleteSuccess"));
      proTableRef.value.getTableList();
    } else {
      ElMessage.error(t("deviceProtect.maybeLife.message.deleteFailed"));
    }
  } catch (error) {
    ElMessage.error(t("deviceProtect.maybeLife.message.deleteError"));
  }
};

// 批量删除选中行
const deleteSelectedRows = async () => {
  ElMessage.success(t("deviceProtect.maybeLife.message.batchDeleteSuccess"));
  selectedRows.value = [];
  proTableRef.value.getTableList();
};

// 处理行点击事件
const handleRowClick = row => {
  console.log("点击行数据：", row);
};

// 处理选中行变化事件
const handleSelectionChange = rows => {
  selectedRows.value = rows;
};

// 打开导入对话框
const importData = () => {
  importDialogVisible.value = true;
};

// 导出数据
const exportData = async () => {
  try {
    const params = {
      ...proTableRef.value.searchParam,
      page: 1,
      pageSize: exportParam.value.pageSize
    };

    const response = await fetchFilteredData(params);
    const tableData = response.data.list;

    const exportColumns = columns.value.filter(col => !["selection", "operation", "id"].includes(col.type || col.prop));
    const headers = exportColumns.map(col => col.label);

    const dataRows = tableData.map(item => {
      return exportColumns.map(col => {
        if (col.prop === "mcTime" && item[col.prop]) {
          return new Date(item[col.prop]).toLocaleString();
        }
        return item[col.prop] || "";
      });
    });

    const worksheet = XLSX.utils.aoa_to_sheet([headers, ...dataRows]);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, t("deviceProtect.maybeLife.title"));

    const exportDate = new Date().toISOString().slice(0, 10);
    XLSX.writeFile(workbook, `${t("deviceProtect.maybeLife.title")}_${exportDate}.xlsx`);

    ElMessage.success(t("deviceProtect.maybeLife.message.exportSuccess"));
  } catch (error) {
    console.log(error, "导出失败");
    ElMessage.error(t("deviceProtect.maybeLife.message.exportError"));
  }
};

// 导入文件前置处理
const beforeUpload = () => {
  // 这里可以添加文件类型、大小等验证逻辑
  return true;
};

// 开始导入
const startImport = () => {
  uploadRef.value!.submit();
  importDialogVisible.value = false;

  ElMessage.success(t("deviceProtect.maybeLife.import.success"));
  // 刷新表格数据
  proTableRef.value.getTableList();
  // 重新构建树状结构数据
};

// 处理树节点点击事件
const handleNodeClick = (nodeObj: any) => {
  let currentNode = nodeObj;
  for (let index = 0; index < treeArr.length; index++) {
    delete proTableRef.value.searchParam[treeArr[index]];
  }
  while (currentNode) {
    const nodeData = currentNode.data;
    if (nodeData && nodeData.type && nodeData.name) {
      // 以节点的type作为键，name作为值
      proTableRef.value.searchParam[nodeData.type] = nodeData.name;
    }
    currentNode = currentNode.parent;
  }
  proTableRef.value.search();
  // const obj = clearObjectValues(initParam.value);
  // initParam.value = { ...obj, ...initParam1, Type: initParam.value.Type };
};

onMounted(async () => {
  const query = {
    Type: 442,
    IsPage: false
  };
  const { data } = await productionReportCapacityApi.getListMesReportData(query);
  dateData.value = convertCalendarData(data.list);
});
</script>

<style scoped>
:deep(.filter) {
  width: auto !important;
}
.button-group {
  display: flex;
  flex-wrap: nowrap;
  gap: 8px;
  align-items: center;
}
.operation-buttons {
  display: flex;
  flex-wrap: nowrap;
  gap: 4px;
  align-items: center;
}
.dialog-footer {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

/* 自定义树的样式 */
.custom-tree {
  width: 20%;
  padding: 10px;
  margin-right: 20px;
  overflow-y: auto;
  background-color: #fafafa;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgb(0 0 0 / 10%);
}

/* 树节点样式 */
.custom-tree .el-tree-node__content {
  height: 32px;
  padding: 0 8px;
  font-size: 14px;
  line-height: 32px;
  color: #333333;
  border-radius: 4px;
  transition: background-color 0.3s;
}

/* 鼠标悬停节点样式 */
.custom-tree .el-tree-node__content:hover {
  background-color: #eef1f6;
}

/* 选中节点样式 */
.custom-tree .el-tree-node.is-current > .el-tree-node__content {
  color: #ffffff;
  background-color: #409eff;
}

/* 展开/收缩图标样式 */
.custom-tree .el-tree-node__expand-icon {
  font-size: 12px;
  color: #909399;
}

/* 展开图标旋转动画 */
.custom-tree .el-tree-node__expand-icon.expanded {
  transition: transform 0.3s;
  transform: rotate(90deg);
}

/* 确保按钮文字不换行 */
:deep(.el-button) {
  white-space: nowrap;
}

/* 调整表格操作列宽度 */
:deep(.el-table .cell) {
  white-space: nowrap;
}
.form-dialog {
  :deep(.el-form-item__label) {
    padding-right: 12px;
    white-space: nowrap;
  }
  :deep(.el-form-item__content) {
    flex: 1;
  }
}
:deep(.el-button) {
  white-space: nowrap;
}
:deep(.el-table .cell) {
  white-space: nowrap;
}
:deep(.el-select) {
  width: 100%;
}
:deep(.el-date-editor) {
  width: 100%;
}
:deep(.el-input-number) {
  width: 100%;
}
</style>
