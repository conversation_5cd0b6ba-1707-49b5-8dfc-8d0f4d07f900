export default {
  home: {
    welcome: "Welcome"
  },
  common: {
    search: "Search",
    reset: "Reset",
    refresh: "Refresh",
    expand: "Expand",
    collapse: "Collapse",
    noData: "No Data",
    today: "Today",
    thisMonth: "This Month",
    thisYear: "This Year",
    compare: "Compare",
    exitCompare: "Exit Compare",
    switchToOverview: "Switch to Overview Compare",
    overviewCompare: "Overview Compare",
    switchToTime: "Switch to Time Compare",
    timeCompare: "Time Compare",
    switchToChart: "Switch to Chart",
    switchToTable: "Switch to Table",
    tableExport: "Table export",
    imageExport: "Image export",
    compareList: {
      processed: "Processed",
      overdue: "Overdue",
      expiring: "Expiring",
      withinDays: "Within 15 Days",
      dailyRequired: "Daily Required",
      DailyMaintenanceCount: "Daily Required",
      MaterialCount: "Raw Material Count",
      production: "Production Count",
      defective: "Defective Count",
      rate: "Yield Rate",
      ngCount: "NG Count",
      ngRate: "NG Rate",
      power: "Power",
      flow: "Flow",
      pressure: "Pressure",
      temperature: "Temperature",
      humidity: "Humidity",
      achievement: "Achievement Rate",
      plan: "Planned Quantity",
      actual: "Actual Quantity",
      mttr: "MTTR",
      mtbf: "MTBF",
      mttr_minutes: "MTTR",
      mtbf_minutes: "MTBF",
      totalFaultTime: "Total Fault Time",
      total_fault_time_minutes: "Total Fault Time",
      fault_count: "Fault Count",
      failure_rate: "Failure Rate",
      faultCount: "Fault Count",
      failureRate: "Failure Rate",
      minutes: "Minutes",
      times: "Times",
      percentage: "%",
      faultStatistics: "Fault Statistics",
      实际产出: "Actual Output",
      计划标准: "Planned Standard",
      达成率: "Achievement Rate",
      OEE: "OEE",
      timeUtilization: "Time Utilization",
      performanceUtilization: "Performance Utilization",
      total_electric: "Total Electric Consumption",
      total_gas: "Total Gas Flow Consumption",
      avg_electric: "Average Electric Consumption",
      avg_gas: "Average Gas Consumption",
      consumptionQuantity: "Consumption quantity",
      gasEnergy: "Gas energy",
      ProcessedCount: "Processed",
      OverdueCount: "Overdue",
      AlmostDueCount: "Expiring Soon",
      wearParts: "Wear Parts",
      count: "Count",
      maintenance1: "Maintenance",
      rawMaterialWarning: "Raw Material Warning",
      time: "Time",
      machine: "Machine",
      averageOEE: "AverageOEE",
      ratio: "Ratio",
      averageAchievementRate: "AverageAchievementRate",
      quantityOrRatio: "QuantityOrRatio",
      averageNGRate: "AverageNGRate",
      NGTimes: "NGTimes",
      numberOfStates: "Number of states",
      totalUnprocessedQuantity: "Total unprocessed quantity",
      unprocessed: "Unprocessed",
      lines: "line",
      Machine: "Machine"
    }
  },
  tabs: {
    refresh: "Refresh",
    maximize: "Maximize",
    closeCurrent: "Close current",
    closeLeft: "Close Left",
    closeRight: "Close Right",
    closeOther: "Close other",
    closeAll: "Close All"
  },
  header: {
    componentSize: "Component size",
    language: "Language",
    theme: "theme",
    layoutConfig: "Layout config",
    primary: "primary",
    darkMode: "Dark Mode",
    greyMode: "Grey mode",
    weakMode: "Weak mode",
    fullScreen: "Full Screen",
    exitFullScreen: "Exit Full Screen",
    personalData: "Personal Data",
    changePassword: "Change Password",
    logout: "Logout",
    weekdays: ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"]
  },
  menu: {
    dashboard: "Dashboard",
    productionReport: "Production Report",
    capacity: "Capacity",
    overallEfficiency: "Overall Efficiency",
    planCompletionRate: "Plan Completion Rate",
    ngStatistics: "NG Statistics",
    productionSummary: "Production Summary",
    equipmentAlarmAnalysis: "Equipment Alarm Analysis",
    alarmReportAndStatistics: "Alarm Report and Statistics",
    lineSummary: "Line Summary",
    faultCount: "Fault Count",
    faultDuration: "Fault Duration",
    faultType: "Fault Type",
    mttrMtbfFaultCount: "MTTR, MTBF, Fault Count",
    historicalFaultRecord: "Historical Fault Record",
    faultSummary: "Fault Summary",
    statusMonitoring: "Status Monitoring",
    currentStatus: "Current Status",
    operationStatus: "Operation Status",
    historicalOperationStatus: "Historical Operation Status",
    equipmentPreventiveMaintenance: "Equipment Preventive Maintenance",
    equipmentAlarmDistribution: "Equipment Alarm Distribution",
    cylinderActionTimeMonitoring: "Cylinder Action Time Monitoring",
    modulePartsRealTimeStatus: "Module Parts Real-time Status",
    theoreticalLifeWarning: "Theoretical Life Warning",
    experienceLifeWarning: "Experience Life Warning",
    dailyMaintenanceList: "Daily Maintenance List",
    historicalMaintenanceRecord: "Historical Maintenance Record",
    historicalWearPartsWarningList: "Historical Wear Parts Warning List",
    energyConsumption: "Energy Consumption",
    realTimeEnergyConsumption: "Real-time Energy Consumption",
    energyConsumptionStatistics: "Energy Consumption Statistics",
    energyConsumptionOverview: "Energy Consumption Overview",
    rawMaterialWarning: "Raw Material Warning",
    rawMaterialEntry: "Raw Material Entry",
    rawMaterialLossStatistics: "Raw Material Loss Statistics",
    productionLineMonitoring: "Production Line Monitoring",
    monitoringScreen: "Monitoring Screen",
    eventAnalysis: "Event Analysis",
    videoPlayback: "Video Playback",
    operationVideoAnalysis: "Operation Video Analysis",
    monitoringVideoStorage: "Monitoring Video Storage",
    companyStructure: "Company Structure",
    organizationManagement: "Organization Management",
    personnelManagement: "Personnel Management",
    positionManagement: "Position Management",
    roleManagement: "Role Management",
    systemSettings: "System Settings",
    warningConfiguration: "Warning Configuration",
    warningUser: "Warning User",
    warningMenu: "Warning Menu",
    management: {
      title: "Menu List",
      menuName: "Menu Name",
      isWarning: "Warning Status",
      warningUser: "Warning User",
      warningTime: "Warning Time",
      operation: "Operation",
      addRow: "Add Row",
      deleteRow: "Delete Row",
      save: "Save",
      saveAll: "Save All",
      timeSetting: {
        title: "Time Point Settings",
        notice: "⚠️ Push will start 10 minutes in advance",
        addTimePoint: "Add Time Point",
        daily: "Daily",
        weekly: "Weekly",
        monthly: "Monthly",
        selectWeek: "Select Week:",
        selectDate: "Select Date:",
        day: "Day",
        week: "Week",
        modify: "Modify",
        add: "Add",
        cancel: "Cancel",
        confirm: "Confirm",
        selectTime: "Select Time",
        fillAllTimePoints: "Please fill in all time points"
      }
    }
  },
  metrics: {
    overallEfficiency: "Overall Efficiency",
    timeEfficiency: "Time Efficiency",
    performanceEfficiency: "Performance Efficiency"
  },
  dashboard: {
    overallEfficiency: "Overall Efficiency",
    deviceStatus: "Device Status",
    energyConsumption: "Energy Consumption",
    deviceEnvironmentalControl: "Device Environmental Control",
    rawMaterialWarning: "Raw Material Warning",
    preventiveMaintenanceWarning: "Preventive Maintenance Warning",
    alarmInformation: "Alarm Information",
    rightTop: {
      materialCount: "Raw Material Count",
      overdueUnprocessed: "Overdue Unprocessed",
      expiringSoon: "Expiring Soon (30min)"
    },
    centerCenter: {
      yieldRate: "Yield Rate",
      goodProductTotal: "Good Product Total",
      ppm: "PPM",
      ngCount: "NG Count",
      productionTotal: "Production Total",
      yearOnYear: "Year on Year",
      completionRate: "Completion Rate",
      ngRate: "NG Rate",
      lossRate: "Loss Rate"
    },
    leftCenter: {
      currentStatusMode: "Current Status Mode",
      cumulativeTimeMode: "Cumulative Time Mode"
    },
    maintenance: {
      title: "Maintenance Calendar",
      types: {
        repair: "Repair",
        inspection: "Inspection",
        maintenance: "Maintenance",
        measurement: "Measurement"
      },
      wearParts: "Wear Parts",
      maintenance1: "Maintenance",
      processed: "Processed",
      overdueUnprocessed: "Overdue Unprocessed",
      expiringSoon: "Expiring Soon",
      withinDays: "Within 15 Days",
      dailyRequired: "Daily Required",
      statisticsMode: "Statistics Mode",
      calendarMode: "Calendar Mode"
    },
    metrics: {
      realTimePower: "Real-time Power",
      todayTotalPower: "Today's Total Power",
      realTimeFlow: "Real-time Flow",
      todayTotalConsumption: "Today's Total Consumption"
    }
  },
  chart: {
    faultTypeCount: "Machine Fault Type Count Ranking",
    faultCount: "Machine Fault Count Ranking",
    faultRate: "Machine Fault Rate Ranking",
    faultCountUnit: "Fault Count",
    faultRateUnit: "Fault Rate (%)",
    faultType: "Fault Type",
    count: "Count"
  },
  productionReport: {
    capacity: {
      title: "Production Statistics Dashboard",
      totalProduction: "Total Production",
      productionCount: "Production Count",
      defectiveCount: "Defective Count",
      yieldRate: "Yield Rate",
      time: "Time",
      productionQuantity: "Production Quantity",
      yieldRateUnit: "Yield Rate (%)",
      yieldRatePercentage: "Yield Rate (%)",
      switchToLine: "Switch to Line Chart",
      switchToBar: "Switch to Bar Chart",
      averageOEE: "Average OEE",
      compareList: {
        production: "Total Production",
        defective: "NG Count",
        rate: "Yield Rate",
        okCount: "OK Count",
        ngCount: "NG Count",
        totalCount: "Total Production"
      }
    }
  },
  statusMonitoring: {
    currentStatus: {
      title: "Current Status",
      exportTable: "Export Table",
      exportImage: "Export Image",
      machineStatus: "Machine Status",
      machineStatusDetail: "Machine Status Details",
      machine: "Machine",
      currentStatus: "Current Status",
      exportSuccess: "Images exported successfully",
      exportError: "Failed to export images, please check error message",
      realTime: {
        title: "Current Machine Status",
        exportExcel: "Export Excel",
        status: "Status",
        machineStatus: "Machine Status"
      },
      stack: {
        title: "Today's Machine Status",
        switchToLine: "Switch to Line Chart",
        switchToBar: "Switch to Bar Chart",
        exportExcel: "Export Excel",
        totalDuration: "Total Duration",
        unit: "Unit/Minute",
        yAxisName: "Cumulative Duration (minutes)",
        subtext: "Current Status"
      }
    }
  },
  alarmDistribution: {
    deviceNames: {
      bUnwinding: "B Unwinding",
      aUnwinding: "A Unwinding",
      bDieCutting: "B Die Cutting",
      aDieCutting: "A Die Cutting",
      cStacking: "C Stacking",
      dStacking: "D Stacking",
      gDischarging: "G Discharging"
    }
  },
  deviceProtect: {
    title: "Device Protection",
    organizationList: "Organization List",
    import: "Import",
    selectFile: "Select File",
    startImport: "Start Import",
    cancel: "Cancel",
    importSuccess: "Import Successful",
    dailyProtect: {
      title: "Daily Maintenance",
      organizationList: "Organization List",
      add: "Add",
      import1: "Import",
      export: "Export",
      batchDelete: "Batch Delete",
      edit: "Edit",
      delete: "Delete",
      view: "View Details",
      close: "Close",
      save: "Save",
      table: {
        id: "ID",
        time: "Time",
        startDate: "Start Date",
        endDate: "End Date",
        operation: "Operation"
      },
      form: {
        time: "Time",
        factory: "Factory",
        workshop: "Workshop",
        productionLine: "Production Line",
        machine: "Machine",
        machineCode: "Machine Code",
        workstation: "Workstation",
        workstationCode: "Workstation Code",
        maintenanceOrg: "Maintenance/Inspection Organization",
        maintenanceItem: "Maintenance/Inspection Item",
        maintenanceStandard: "Maintenance/Inspection Standard",
        maintenanceMethod: "Maintenance/Inspection Method",
        maintenanceFrequency: "Maintenance/Inspection Frequency",
        maintenanceStatus: "Maintenance/Inspection Status",
        maintenancePerson: "Maintenance/Inspection Person",
        maintenanceTime: "Maintenance/Inspection Time",
        remarks: "Remarks",
        selectMachine: "Please select machine",
        selectMachineCode: "Please select machine code"
      },
      status: {
        completed: "Completed",
        uncompleted: "Uncompleted",
        inProgress: "In Progress"
      },
      importData: {
        title: "Import Data",
        selectFile: "Select File",
        startImport: "Start Import",
        cancel: "Cancel",
        success: "Import Successful"
      },
      validation: {
        required: "Please fill in required fields",
        factory: "Please fill in factory",
        workshop: "Please fill in workshop",
        productionLine: "Please fill in production line"
      }
    },
    deviceTypes: {
      motor: "Motor",
      cylinder: "Cylinder",
      module: "Module",
      cutter: "Cutter"
    },
    deviceInfo: {
      factory: "Factory",
      workshop: "Workshop",
      productionLine: "Production Line",
      materialCode: "Material Code",
      materialName: "Material Name",
      specModel: "Specification Model",
      brand: "Brand",
      singlePartQty: "Single Part Quantity",
      unit: "Unit",
      parentMaterialCode: "Parent Material Code",
      parentMaterialName: "Parent Material Name",
      workstation: "Workstation",
      installTime: "Installation Time",
      prodRunCountOrDur: "Production Run Count/Duration",
      theoreticalLife: "Theoretical Life",
      usedLife: "Used Life",
      remainingLife: "Remaining Life",
      remarks: "Remarks",
      noRemarks: "None"
    },
    alertTable: {
      title: "Pre-alarm",
      time: "Time",
      message: "Message",
      operation: "Operation",
      handle: "Handle",
      ignore: "Ignore",
      status: "Status",
      statusTypes: {
        pending: "Pending",
        processing: "Processing",
        completed: "Completed"
      }
    },
    maybeLife: {
      title: "Device Life Warning",
      organizationList: "Organization List",
      add: "Add",
      import: "Import",
      export: "Export",
      batchDelete: "Batch Delete",
      edit: "Edit",
      delete: "Delete",
      view: "View Details",
      close: "Close",
      save: "Save",
      form: {
        number: "Number",
        time: "Time",
        factory: "Factory",
        workshop: "Workshop",
        productionLine: "Production Line",
        machine: "Machine",
        workstation: "Workstation",
        component: "Component",
        workstationCategory: "Workstation Category",
        materialCode: "Material Code",
        materialName: "Material Name",
        materialSpecification: "Material Specification",
        brand: "Brand",
        theoreticalLife: "Theoretical Life",
        experienceLife: "Experience Life",
        usedLife: "Used Life",
        lifeUnit: "Life Unit",
        lifeWarningLowerLimit: "Life Warning Lower Limit",
        lifeWarningUpperLimit: "Life Warning Upper Limit",
        warningTime: "Warning Time",
        warningStatus: "Warning Status",
        processingStatus: "Processing Status",
        processor: "Processor",
        processingTime: "Processing Time",
        processingDuration: "Processing Duration",
        remarks: "Remarks",
        operation: "Operation",
        selectMachine: "Please select machine"
      },
      status: {
        normal: "Normal",
        warning: "Warning",
        warningInProgress: "Warning in Progress",
        danger: "Danger",
        unprocessed: "Unprocessed",
        processing: "Processing",
        processed: "Processed"
      },
      importData: {
        title: "Import Data",
        selectFile: "Select File",
        startImport: "Start Import",
        cancel: "Cancel",
        success: "Import Successful"
      },
      validation: {
        required: "Please fill in required fields",
        processingTime: "Please select processing time",
        theoreticalLife: "Please fill in theoretical life",
        experienceLife: "Please fill in experience life",
        usedLife: "Please fill in used life",
        lifeWarningLowerLimit: "Please fill in life warning lower limit",
        lifeWarningUpperLimit: "Please fill in life warning upper limit",
        warningTime: "Please select warning time"
      },
      message: {
        deleteSuccess: "Delete Successful",
        deleteFailed: "Delete Failed",
        deleteError: "Error occurred during deletion",
        batchDeleteSuccess: "Batch Delete Successful",
        exportSuccess: "Export Successful",
        exportError: "Export Failed"
      }
    }
  },
  energyConsumption: {
    current: {
      title: "Real-time Energy Consumption",
      exportTable: "Export Table",
      exportImage: "Export Image",
      exportSuccess: "Images exported successfully",
      exportError: "Failed to export images, please check error message",
      electricity: {
        title: "Electricity Statistics",
        realTimePower: "Real-time Power Consumption",
        totalPower: "Total Power Consumption",
        powerConsumption: "Power Consumption",
        energyConsumption: "Energy Consumption",
        unit: "kWh",
        switchToBar: "Switch to Bar Chart",
        switchToLine: "Switch to Line Chart",
        exportExcel: "Export Excel"
      },
      gas: {
        title: "Gas Statistics",
        realTimeFlow: "Real-time Gas Flow",
        totalFlow: "Total Gas Flow",
        realTimeGas: "Real-time Gas",
        averageFlow: "Average Flow",
        totalConsumption: "Total Consumption",
        unit: "m³",
        switchToBar: "Switch to Bar Chart",
        switchToLine: "Switch to Line Chart",
        exportExcel: "Export Excel"
      }
    }
  },
  rawMaterialWarning: {
    title: "Raw Material Warning Calendar",
    organizationList: "Organization List",
    add: "Add",
    import1: "Import",
    export: "Export",
    batchDelete: "Batch Delete",
    edit: "Edit",
    delete: "Delete",
    view: "View Details",
    close: "Close",
    save: "Save",
    operation: "Operation",
    attritionStatistics: {
      title: "Raw Material Usage Calendar"
    },
    form: {
      factory: "Factory",
      workshop: "Workshop",
      productionLine: "Production Line",
      machineCode: "Machine Code",
      machineName: "Machine Name",
      stationCode: "Station Code",
      stationName: "Station Name",
      materialType: "Material Type",
      batch: "Material Batch",
      materialCode: "Material Code",
      materialName: "Material Name",
      unit: "Material Unit",
      totalQuantity: "Total Quantity",
      usedQuantity: "Used Quantity",
      remainingQuantity: "Remaining Quantity",
      isReplaced: "Is Material Replaced",
      feedingTime: "Feeding Time",
      operator: "Operator Name",
      operationTime: "Operation Time"
    },
    status: {
      yes: "Yes",
      no: "No"
    },
    importData: {
      title: "Import Data",
      selectFile: "Select File",
      startImport: "Start Import",
      cancel: "Cancel",
      success: "Import Successful"
    },
    validation: {
      required: "Please fill in required fields",
      factory: "Please fill in factory",
      workshop: "Please fill in workshop",
      productionLine: "Please fill in production line",
      machineCode: "Please fill in machine code",
      machineName: "Please fill in machine name",
      stationCode: "Please fill in station code",
      stationName: "Please fill in station name",
      materialType: "Please fill in material type",
      batch: "Please fill in material batch",
      materialCode: "Please fill in material code",
      materialName: "Please fill in material name",
      unit: "Please fill in material unit"
    },
    message: {
      addSuccess: "Added successfully",
      editSuccess: "Edited successfully",
      deleteSuccess: "Deleted successfully",
      batchDeleteSuccess: "Batch deleted successfully",
      importSuccess: "Imported successfully",
      exportSuccess: "Exported successfully",
      operationFailed: "Operation failed",
      networkError: "Network request failed, please try again",
      selectDelete: "Please select records to delete",
      requiredFields:
        "Factory, workshop, production line, machine code, machine name, station code, station name, material type, material batch, material code, material name, and material unit are required"
    },
    rules: {
      factory: "Please fill in the factory",
      workshop: "Please fill in the workshop",
      productionLine: "Please fill in the line body.",
      machineCode: "Please fill in the machine code.",
      machineName: "Please fill in the name of the machine",
      stationCode: "Please fill in the workstation code",
      stationName: "Please fill in the workstation name",
      materialType: "Please fill in the type of raw material",
      batch: "Please fill in the batch of raw materials",
      materialCode: "Please fill in the raw material code",
      materialName: "Please fill in the name of the raw material",
      unit: "Please fill in the unit of raw materials"
    }
  },
  user: {
    title: "User List",
    organizationList: "Organization List",
    import: "Import",
    export: "Export",
    selectFile: "Select File",
    startImport: "Start Import",
    cancel: "Cancel",
    sure: "Sure",
    importSuccess: "Import Successful",
    form: {
      nameOrAccount: "Name or Account",
      avatar: "Avatar",
      account: "Account",
      name: "Name",
      gender: "Gender",
      phone: "Phone",
      organization: "Organization",
      position: "Position",
      createTime: "Create Time",
      operation: "Operation",
      editor: "Editor",
      delete: "Delete",
      user: "User",
      mores: "More",
      basic: {
        basicInformation: "Basic information",
        nameOrAccount: "Name or Account",
        avatar: "Avatar",
        account: "Account",
        name: "Name",
        gender: "Gender",
        phone: "Phone",
        organization: "Organization",
        position: "Position",
        createTime: "Create Time",
        operation: "Operation",
        nickname: "Nickname",
        email: "Email",
        birthday: "Birthday",
        entryDate: "Entry Date",
        empNo: "Employee Number",
        positionLevel: "Position Level",
        orgAndPosIdList: "Department and Position",
        directorId: "Director",
        choose: "Choose",
        selector: {
          title: "Selection",
          orgTab: "Organization",
          positionTab: "Position",
          roleTab: "Role",
          list: "List",
          positionTree: "Position List",
          roleTree: "Role List",
          addCurrent: "Add Current",
          addSelected: "Add Selected",
          delCurrent: "Delete Current",
          delSelected: "Delete Selected",
          selectedCount: "Selected",
          maxCount: "Max Select",
          personUnit: "person(s)",
          addButton: "Add",
          delButton: "Delete",
          cancelButton: "Cancel",
          confirmButton: "Confirm"
        }
      },
      more: {
        moreInformation: "More information",
        nation: "Nation",
        nativePlace: "Native Place",
        homeAddress: "Home Address",
        mailingAddress: "Mailing Address",
        idCardType: "ID Type",
        idCardNumber: "ID Number",
        cultureLevel: "Education Level",
        politicalOutlook: "Political Status",
        college: "Graduated From",
        education: "Education",
        eduLength: "Duration of Study",
        degree: "Degree",
        homeTel: "Home Tel",
        officeTel: "Office Tel",
        emergencyContact: "Emergency Contact",
        emergencyPhone: "Emergency Phone",
        emergencyAddress: "Emergency Address"
      }
    },

    more: {
      resetPassword: "Reset Password",
      grantRole: "Grant Role",
      grantResource: "Grant Resource",
      grantPermission: "Grant Permission"
    },
    resource: {
      NonOvermanaged: "Non-overmanaged roles cannot be authorized for system module menu resources",
      pleaseSelect: "Please select the data range",
      parentName: "First-level directory",
      title: "Menu",
      button: "Button authorization"
    },
    Permission: {
      note: "Note: This functional interface needs to be used in conjunction with code query conditions. Not all interfaces require data range Settings. It is mostly used in business modules!",
      api: "Interface",
      dataScope: "Data range",
      interfaceName: "Please enter the interface name"
    },
    grantRole: {
      title: "Global",
      name: "Character name",
      operation: "operation",
      orgTab: "Organization",
      positionTab: "Position",
      roleTab: "Role selection",
      organizationList: "Organization list",
      positionTree: "Position List",
      roleTree: "Role List",
      addCurrent: "Add Current",
      addSelected: "Add Selected",
      delCurrent: "Delete Current",
      delSelected: "Delete Selected",
      selectedCount: "Selected",
      maxCount: "Max Select",
      personUnit: "person(s)",
      addButton: "Add",
      delButton: "Delete",
      cancelButton: "Cancel",
      confirmButton: "Confirm"
    }
  },
  calendar: {
    weekdays: ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"],
    tooltip: {
      title: "Maintenance Details",
      count: "Count",
      percentage: "Percentage"
    }
  },
  alarmRealtime: {
    title: "Alarm Real-time Analysis",
    exportTable: "Export Table",
    exportImage: "Export Image",
    exportData: "Export Data",
    time1: "Time",
    time: {
      title: "Fault Count",
      machineName: "Machine Name",
      faultCount: "Fault Count",
      switchToBar: "Switch to Bar Chart",
      switchToLine: "Switch to Line Chart",
      exportExcel: "Export Excel",
      exportFileName: "Production Line Fault Duration"
    },
    long: {
      title: "Fault Duration",
      machineName: "Machine Name",
      duration: "Fault Duration",
      durationMinutes: "Fault Duration (minutes)",
      switchToBar: "Switch to Bar Chart",
      switchToLine: "Switch to Line Chart",
      exportExcel: "Export Excel",
      exportFileName: "Real-time Fault Duration"
    },
    alarmRate: {
      title: "Fault Rate",
      machineName: "Machine Name",
      rate: "Fault Rate",
      ratePercentage: "Fault Rate (%)",
      switchToBar: "Switch to Bar Chart",
      switchToLine: "Switch to Line Chart",
      exportExcel: "Export Excel",
      exportFileName: "Production Line Fault Rate"
    },
    machine: "Machine",
    currentAlarm: {
      title: "Current Alarm",
      factory: "Factory Name",
      workshop: "Workshop Name",
      productionLine: "Production Line",
      machine: "Machine Name",
      station: "Station Name",
      faultType: "Fault Type",
      faultName: "Fault Name",
      alarmInfo: "Alarm Info",
      startTime: "Start Time",
      duration: "Duration (min)",
      handler: "Handler",
      status: "Status"
    },
    charts: {
      time: "Fault Count",
      long: "Fault Duration",
      alarmRate: "Fault Rate",
      mttr: "Mean Time To Repair",
      currentAlarm: "Current Alarm"
    },
    export: {
      success: "Images exported successfully",
      error: "Failed to export images, please check error message"
    },
    mttr: {
      title: "MTTR and MTBF",
      machineName: "Machine Name",
      mttr: "MTTR",
      mtbf: "MTBF",
      timeHours: "Time (hours)",
      hours: "hours",
      switchToBar: "Switch to Bar Chart",
      switchToLine: "Switch to Line Chart",
      exportExcel: "Export Excel",
      exportFileName: "Production Line MTTR & MTBF"
    }
  },
  deviceStatus: {
    running: "Running",
    waiting: "Waiting",
    stopped: "Stopped",
    fault: "Fault",
    maintenance: "Maintenance",
    unknownStatus: "Unknown Status",
    minutes: "min",
    windSpeed: "Wind Speed",
    metersPerSecond: "m/s",
    concentration: "Concentration",
    ppm: "ppm",
    needsMaintenance: "needs maintenance",
    maintenanceInProgress: "Engineer Wang is repairing",
    deviceAging: "Device aging"
  },
  sheetCount: {
    station: "Station Name",
    alarmName: "Alarm Name",
    alarmInfo: "Alarm Info",
    startTime: "Start Time",
    duration: "Duration",
    faultCountTop10: "Fault Count Top10",
    faultCount: "Fault Count",
    faultTypeTop10: "Fault Type Top10",
    faultType: "Fault Type",
    count: "Count",
    faultTypeCount: "Fault Type Count",
    faultRate: "Fault Rate",
    faultDurationStats: "Machine Fault Duration Statistics"
  },
  alarmHistory: {
    title: "Fault Calendar This Month",
    organizationList: "Organization List",
    export: "Export",
    view: "View",
    close: "Close",
    save: "Save",
    form: {
      factory: "Factory",
      workshop: "Workshop",
      productionLine: "Production Line",
      machine: "Machine",
      station: "Station",
      faultType: "Fault Type",
      faultName: "Fault Name",
      alarmInfo: "Alarm Info",
      startTime: "Fault Start Time",
      durationMinutes: "Fault Duration (minutes)"
    },
    dialog: {
      add: "Add Fault Record",
      edit: "Edit Fault Record",
      view: "Fault Record Details"
    },
    table: {
      id: "ID",
      time: "Time",
      factory: "Factory",
      workshop: "Workshop",
      productionLine: "Production Line",
      machine: "Machine",
      station: "Station",
      faultType: "Fault Type",
      faultName: "Fault Name",
      alarmInfo: "Alarm Info",
      startTime: "Fault Start Time",
      durationMinutes: "Fault Duration (minutes)",
      operation: "Operation"
    },
    exportData: {
      success: "Export Successful",
      error: "Export Failed"
    }
  },
  treeFilter: {
    text: "Please enter keywords for filtering",
    expandAll: "Expand all",
    collapseAll: "Fold all",
    all: "All"
  }
};
