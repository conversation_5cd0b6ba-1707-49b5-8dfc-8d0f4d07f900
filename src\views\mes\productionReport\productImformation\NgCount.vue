<template>
  <ProChart
    :options="chartOptions"
    :fetch-api="fetchData"
    :tool-buttons="['refresh', 'download', 'table', 'compare']"
    @data-loaded="handleDataLoaded"
    :compare-list="compareList"
    :machines="machines"
    :init-params="initParams"
    ref="userTable"
    @export-data="handleExportData"
  />
</template>

<script setup lang="tsx">
import moment from "moment";
import { productionReportCapacityApi } from "@/api";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
const initParams = ref({
  time: [moment().startOf("day").format("YYYY-MM-DD"), moment().endOf("day").format("YYYY-MM-DD")],
  type: 4
});
const machines = ref<any[]>([]);
const ngTypes = ref<string[]>([]);
const colorPalette = ref<string[]>([]);

// 动态生成颜色配置
const generateColors = (count: number) => {
  const palette = [
    "#00bfff",
    "#ff4d4f",
    "#fac858",
    "#87d068",
    "#9a60b4",
    "#ff8c00",
    "#1e90ff",
    "#32cd32",
    "#ff1493",
    "#7b68ee",
    "#00fa9a",
    "#ff6347"
  ];
  return palette.slice(0, count);
};
const root = document.documentElement;
const chartColor = getComputedStyle(root).getPropertyValue("--el-text-color-regular");
// 图表基础配置
const baseChartOptions = {
  title: [
    // 主标题 - NG统计
    {
      text: t("menu.ngStatistics"),
      left: "6%",
      top: 0,
      textStyle: {
        fontSize: 16,
        fontWeight: "bold",
        color: chartColor
      }
    },
    // 副标题 - 平均NG率：0
    {
      subtext: t("common.compareList.averageNGRate") + ": 0", // 平均NG率：0
      left: "center",
      top: -10, // 适当调整位置使其在主标题下方
      textStyle: {
        fontSize: 16,
        color: chartColor
      },
      subtextStyle: {
        color: chartColor
      }
    }
  ],
  // title: {
  //   subtext: t("common.compareList.averageNGRate") + ": 0", //"平均NG率：0",
  //   left: "center",
  //   textStyle: {
  //     fontSize: 16,
  //     color: chartColor
  //   },
  //   subtextStyle: {
  //     color: chartColor
  //   },
  //   top: -12
  // },
  toolbox: {
    show: true,
    feature: {
      magicType: {
        type: ["line", "bar"],
        title: {
          line: t("productionReport.capacity.switchToLine"), // 切换为折线图
          bar: t("productionReport.capacity.switchToBar") // 切换为柱状图
        }
      },
      saveAsImage: { show: true }
    }
  },
  tooltip: {
    trigger: "axis",
    axisPointer: { type: "shadow" }
  },
  legend: {
    top: 13,
    textStyle: { color: chartColor }
  },
  xAxis: {
    type: "category",
    data: [],
    axisLabel: {
      color: chartColor,
      interval: 0,
      rotate: 45
    }
  },
  yAxis: [
    {
      type: "value",
      name: t("common.compareList.ratio"), //"比率 (%)",
      min: 0,

      axisLabel: {
        color: chartColor,
        formatter: (value: number) => `${value}%`
      },
      splitLine: { lineStyle: { color: "#eee" } }
    }
  ],
  grid: {
    left: "3%",
    right: "3%",
    bottom: "3%",
    containLabel: true
  },
  series: []
};

const chartOptions = ref({ ...baseChartOptions });

// 动态生成对比列表
const compareList = computed(() => {
  return ngTypes.value.map(type => ({
    label: type,
    value: type
  }));
});

// 数据转换函数
function transformData(responseData, timeType) {
  const machineMap = new Map();
  const timeSet = new Set();
  const machineIdSet = new Set();
  const ngTypeSet = new Set();

  let formatPattern;
  switch (timeType) {
    case "Hour":
      formatPattern = "HH:00";
      break;
    case "Mon":
      formatPattern = "MM-DD";
      break;
    case "Year":
      formatPattern = "YYYY-MM";
      break;
    default:
      formatPattern = "HH:00";
  }

  // 遍历数据，构建数据结构
  responseData.forEach(item => {
    const machine = item.machine;
    const ngType = item.ngItem;
    const formattedTime = moment(item.start_time).format(formatPattern);

    if (!machineMap.has(machine)) {
      machineMap.set(machine, new Map());
    }

    const machineData = machineMap.get(machine);
    if (!machineData.has(ngType)) {
      machineData.set(ngType, {
        dataPoints: [],
        total: 0,
        count: 0
      });
    }

    const ngTypeData = machineData.get(ngType);
    ngTypeData.dataPoints.push({
      time: formattedTime,
      value: item.averageNGRate
    });
    ngTypeData.total += item.averageNGRate;
    ngTypeData.count++;

    timeSet.add(formattedTime);
    machineIdSet.add(machine);
    ngTypeSet.add(ngType);
  });

  // 更新NG类型和颜色
  ngTypes.value = Array.from(ngTypeSet);
  colorPalette.value = generateColors(ngTypes.value.length);

  // 对时间点进行排序
  const categories = Array.from(timeSet).sort();

  // 计算各NG类型的平均值
  machineMap.forEach(machineData => {
    machineData.forEach(ngTypeData => {
      ngTypeData.average = (ngTypeData.total / ngTypeData.count).toFixed(1);
    });
  });

  // 构建对比数据
  const compareData = {};
  let compare = [];

  ngTypes.value.forEach(type => {
    compareData[type] = Array.from(machineMap.entries()).map(([machine, ngData]) => ({
      machine,
      data: ngData.has(type) ? ngData.get(type).dataPoints.map(d => d.value) : [],
      total: ngData.has(type) ? parseFloat(ngData.get(type).average) : 0
    }));
  });
  compare.push(compareData);
  return {
    categories,
    machines: Array.from(machineIdSet).map(id => ({ id, name: id })),
    compareData: compare,
    machineData: machineMap
  };
}

// 数据获取函数
const fetchData = async (params: any) => {
  try {
    const time = {
      StartDate: moment(params.time[0]).format("YYYY-MM-DD HH:mm:ss").toString(),
      EndDate: moment(params.time[1]).set({ hour: 23, minute: 59, second: 59 }).format("YYYY-MM-DD HH:mm:ss").toString()
    };
    const query = {
      ...time,
      type: params.type
    };
    const { data } = await productionReportCapacityApi.getListMesReportData(query);

    if (!data || !data.list || data.list.length === 0) {
      return {
        data: {
          categories: [],
          seriesData: [],
          isCompare: false
        }
      };
    }

    // 获取时间模式
    const mode = data.list[0].type || "Hour";
    const transformedData = transformData(data.list, mode);

    machines.value = transformedData.machines;

    // 普通模式
    if (!params.compareMode) {
      const targetMachine = params.machine || transformedData.machines[0]?.name;
      const machineData = transformedData.machineData.get(targetMachine);

      if (!machineData) {
        return {
          data: {
            categories: transformedData.categories,
            seriesData: [],
            isCompare: false
          }
        };
      }

      const seriesData = ngTypes.value.map(type => (machineData.has(type) ? machineData.get(type).dataPoints.map(d => d.value) : []));

      return {
        data: {
          categories: transformedData.categories,
          seriesData,
          isCompare: false,
          ngTypes: ngTypes.value
        }
      };
    }

    // 对比模式
    return {
      data: {
        isCompare: true,
        categories: transformedData.categories,
        compare: transformedData.compareData,
        ngTypes: ngTypes.value
      }
    };
  } catch (error) {
    console.error("获取数据失败:", error);
    return {
      data: {
        categories: [],
        seriesData: [],
        isCompare: false
      }
    };
  }
};

// 数据加载回调
const handleDataLoaded = (data: any) => {
  // 更新tooltip格式化
  const tooltipFormatter = (params: any) => {
    let html = `<div style="padding:5px;min-width:120px">`;
    params.forEach((p: any) => {
      html += `
        <div>
          <span style="display:inline-block;width:10px;height:10px;background:${p.color};border-radius:50%"></span>
          ${p.seriesName}: ${p.data}%
        </div>`;
    });
    html += `</div>`;
    return html;
  };

  // 普通模式
  if (!data.isCompare) {
    const totalNG = data.seriesData.flat().reduce((sum: number, val: number) => sum + val, 0);
    const count = data.seriesData.flat().length;
    const averageNG = count > 0 ? (totalNG / count).toFixed(1) : "0";

    chartOptions.value = {
      ...baseChartOptions,
      title: [
        // 主标题保持不变
        chartOptions.value.title[0],
        // 只更新副标题的内容
        {
          ...chartOptions.value.title[1],
          subtext: `${t("common.compareList.averageNGRate")}：${averageNG}`
        }
      ],
      // title: {
      //   ...baseChartOptions.title,
      //   subtext: `${t("common.compareList.averageNGRate")}：${averageNG}`
      // },
      xAxis: {
        ...baseChartOptions.xAxis,
        data: data.categories
      },
      legend: {
        ...baseChartOptions.legend,
        data: data.ngTypes || []
      },
      tooltip: {
        ...baseChartOptions.tooltip,
        formatter: tooltipFormatter
      },
      series: (data.ngTypes || []).map((type: string, index: number) => ({
        name: type,
        type: "bar",
        data: data.seriesData[index] || [],
        itemStyle: { color: colorPalette.value[index] },
        label: {
          show: true,
          position: "top",
          formatter: "{c}%"
        }
      }))
    };
  } else {
    // 对比模式处理
    // 这里可以根据需要实现对比模式的图表展示
  }
};
const emits = defineEmits(["data-ready", "export-data"]);

const handleExportData = (csvContent: string) => {
  // 父组件可以在这里做一些处理，然后继续传递给爷组件

  // eslint-disable-next-line vue/custom-event-name-casing
  emits("export-data", {
    data: csvContent
  });
};
const userTable = ref<any>(null);

defineExpose({
  tableRef: userTable
});
</script>
