export default {
  home: {
    welcome: "欢迎使用"
  },
  common: {
    search: "搜索",
    reset: "重置",
    refresh: "刷新",
    expand: "展开",
    collapse: "合并",
    noData: "暂无数据",
    today: "当日",
    thisMonth: "当月",
    thisYear: "当年",
    compare: "对比",
    exitCompare: "退出对比",
    switchToOverview: "切换为总览对比",
    overviewCompare: "总览对比",
    switchToTime: "切换为时间对比",
    timeCompare: "时间对比",
    switchToChart: "切换图表",
    switchToTable: "切换表格",
    tableExport: "表格导出",
    imageExport: "图片导出",
    switchToLine: "切换为折线图",
    switchToBar: "切换为柱状图",
    compareList: {
      processed: "已处理",
      overdue: "超时未处理",
      expiring: "即将到期",
      withinDays: "15天内",
      dailyRequired: "每日需维护",
      DailyMaintenanceCount: "每日需维护",
      MaterialCount: "原材料项数",
      production: "OK计数",
      defective: "NG计数",
      rate: "良率",
      ngCount: "NG数量",
      ngRate: "NG率",
      power: "功率",
      flow: "流量",
      pressure: "压力",
      temperature: "温度",
      humidity: "湿度",
      achievement: "达成率",
      plan: "计划数",
      actual: "实际数",
      mttr: "MTTR",
      mttr_minutes: "MTTR",
      mtbf_minutes: "MTBF",
      mtbf: "MTBF",
      totalFaultTime: "总故障时间",
      total_fault_time_minutes: "总故障时间",
      faultCount: "故障次数",
      fault_count: "故障次数",
      failureRate: "故障率",
      failure_rate: "故障率",
      minutes: "分钟",
      times: "次",
      percentage: "%",
      faultStatistics: "故障统计信息",
      实际产出: "实际产出",
      计划标准: "计划标准",
      达成率: "达成率",
      OEE: "OEE",
      timeUtilization: "时间稼动率",
      performanceUtilization: "性能稼动率",
      total_electric: "总电能消耗",
      total_gas: "总气流量消耗",
      avg_electric: "平均电能消耗",
      avg_gas: "平均气能消耗",
      consumptionQuantity: "消耗数量",
      gasEnergy: "气能",
      ProcessedCount: "已处理",
      OverdueCount: "超时未处理",
      AlmostDueCount: "即将到期",
      wearParts: "易损件",
      count: "数量",
      maintenance1: "维护保养",
      rawMaterialWarning: "原材料预警",
      time: "时间",
      machine: "机台",
      averageOEE: "平均OEE",
      ratio: "比率 (%)",
      averageAchievementRate: "平均达成率",
      quantityOrRatio: "数量/比率 (%)",
      averageNGRate: "平均NG率",
      NGTimes: "NG次数",
      numberOfStates: "状态数量",
      totalUnprocessedQuantity: "总未处理数量",
      unprocessed: "未处理",
      lines: "产线",
      Machine: "机台"
    }
  },
  tabs: {
    refresh: "刷新",
    maximize: "最大化",
    closeCurrent: "关闭当前",
    closeLeft: "关闭左侧",
    closeRight: "关闭右侧",
    closeOther: "关闭其它",
    closeAll: "关闭所有"
  },
  header: {
    componentSize: "组件大小",
    language: "国际化",
    theme: "全局主题",
    layoutConfig: "布局设置",
    primary: "primary",
    darkMode: "暗黑模式",
    greyMode: "灰色模式",
    weakMode: "色弱模式",
    fullScreen: "全屏",
    exitFullScreen: "退出全屏",
    personalData: "个人信息",
    changePassword: "修改密码",
    logout: "退出登录",
    weekdays: ["周日", "周一", "周二", "周三", "周四", "周五", "周六"]
  },
  menu: {
    dashboard: "数据看板",
    productionReport: "生产报表",
    capacity: "产能",
    overallEfficiency: "综合效率",
    planCompletionRate: "计划达成率",
    ngStatistics: "NG统计",
    productionSummary: "生产信息汇总",
    equipmentAlarmAnalysis: "设备告警分析",
    alarmReportAndStatistics: "告警报表与统计",
    lineSummary: "整线汇总",
    faultCount: "故障次数",
    faultDuration: "故障时长",
    faultType: "故障类型",
    mttrMtbfFaultCount: "MTTR、MTBF、故障次数",
    historicalFaultRecord: "历史故障记录",
    faultSummary: "故障汇总",
    statusMonitoring: "状态监控",
    currentStatus: "当前状态",
    operationStatus: "运行状况",
    historicalOperationStatus: "历史运行状况",
    equipmentPreventiveMaintenance: "设备预防性维护",
    equipmentAlarmDistribution: "设备告警分布盘",
    cylinderActionTimeMonitoring: "气缸动作时间监控",
    modulePartsRealTimeStatus: "各模块零部件实时状态",
    theoreticalLifeWarning: "理论经验寿命预警",
    experienceLifeWarning: "经验寿命预警",
    dailyMaintenanceList: "日常保养清单",
    historicalMaintenanceRecord: "历史保养记录",
    historicalWearPartsWarningList: "历史易损件预警清单",
    energyConsumption: "能耗",
    realTimeEnergyConsumption: "实时能耗",
    energyConsumptionStatistics: "电能及气流量能耗统计",
    energyConsumptionOverview: "电能及气流量能耗总览",
    rawMaterialWarning: "原材料预警",
    rawMaterialEntry: "原材料录入",
    rawMaterialLossStatistics: "原材料损耗统计",
    productionLineMonitoring: "产线监控",
    monitoringScreen: "监控大屏",
    eventAnalysis: "事件分析",
    videoPlayback: "视频回放",
    operationVideoAnalysis: "运行视频分析",
    monitoringVideoStorage: "监控视频储存",
    companyStructure: "公司架构",
    organizationManagement: "机构管理",
    personnelManagement: "人员管理",
    positionManagement: "岗位管理",
    roleManagement: "角色管理",
    systemSettings: "系统设置",
    warningConfiguration: "预警配置",
    warningUser: "预警用户",
    warningMenu: "预警菜单",
    management: {
      title: "菜单列表",
      menuName: "菜单名称",
      isWarning: "是否预警",
      warningUser: "预警用户",
      warningTime: "预警时间",
      operation: "操作",
      addRow: "新增行",
      deleteRow: "删除行",
      save: "保存",
      saveAll: "全部保存",
      timeSetting: {
        title: "时间点设置",
        notice: "⚠️ 推送会提前10分钟开始",
        addTimePoint: "添加时间点",
        daily: "每日",
        weekly: "每周",
        monthly: "每月",
        selectWeek: "选择星期：",
        selectDate: "选择日期：",
        day: "号",
        week: "周",
        modify: "修改",
        add: "新增",
        cancel: "取消",
        confirm: "确定",
        selectTime: "选择时间",
        fillAllTimePoints: "请填写所有时间点"
      }
    }
  },
  metrics: {
    overallEfficiency: "综合效率",
    timeEfficiency: "时间稼动率",
    performanceEfficiency: "性能稼动率"
  },
  dashboard: {
    overallEfficiency: "综合效率",
    deviceStatus: "设备状态",
    energyConsumption: "能耗",
    deviceEnvironmentalControl: "设备环控数据",
    rawMaterialWarning: "原材料预警",
    preventiveMaintenanceWarning: "预防性维护预警",
    alarmInformation: "告警信息",
    rightTop: {
      materialCount: "原材料项数",
      overdueUnprocessed: "超时未处理",
      expiringSoon: "即将到期 (30min)"
    },
    centerCenter: {
      yieldRate: "优率",
      goodProductTotal: "良品总数",
      ppm: "PPM",
      ngCount: "NG数",
      productionTotal: "生产总数",
      yearOnYear: "同比",
      completionRate: "完成率",
      ngRate: "NG率",
      lossRate: "折损率"
    },
    leftCenter: {
      currentStatusMode: "当前状态模式",
      cumulativeTimeMode: "累积时长模式"
    },
    maintenance: {
      title: "本月维保日历",
      types: {
        repair: "维修",
        inspection: "检查",
        maintenance: "保养",
        measurement: "测量"
      },
      wearParts: "易损件",
      maintenance1: "维护保养",
      processed: "已处理",
      overdueUnprocessed: "超时未处理",
      expiringSoon: "即将到期",
      withinDays: "15天内",
      dailyRequired: "每日需维护",
      statisticsMode: "统计模式",
      calendarMode: "日历模式"
    },
    metrics: {
      realTimePower: "实时功率",
      todayTotalPower: "今日总耗电",
      realTimeFlow: "实时流量",
      todayTotalConsumption: "今日总耗能"
    }
  },
  chart: {
    faultTypeCount: "机台故障类型次数排序",
    faultCount: "机台故障次数排序",
    faultRate: "机台故障率排序",
    faultCountUnit: "故障次数",
    faultRateUnit: "故障率 (%)",
    faultType: "故障类型",
    count: "次数"
  },
  productionReport: {
    capacity: {
      title: "生产统计看板",
      totalProduction: "总生产量",
      productionCount: "OK计数",
      defectiveCount: "不良计数",
      yieldRate: "良率",
      time: "时间",
      productionQuantity: "生产数量",
      yieldRateUnit: "良率 (%)",
      yieldRatePercentage: "良率 (%)",
      switchToLine: "切换为折线图",
      switchToBar: "切换为柱状图",
      averageOEE: "平均OEE",
      compareList: {
        production: "生产总数",
        defective: "NG数",
        rate: "良率",
        okCount: "OK数",
        ngCount: "NG数",
        totalCount: "生产总数"
      }
    }
  },
  statusMonitoring: {
    currentStatus: {
      title: "当前状态",
      exportTable: "表格导出",
      exportImage: "图片导出",
      machineStatus: "机台状态",
      machineStatusDetail: "机台各状态",
      machine: "机台",
      currentStatus: "当前状态",
      exportSuccess: "图片批量导出成功",
      exportError: "图片批量导出失败，请查看错误提示",
      realTime: {
        title: "当前各机台状态",
        exportExcel: "导出Excel",
        status: "状态",
        machineStatus: "机台状态"
      },
      stack: {
        title: "当天各机台状态",
        switchToLine: "切换为折线图",
        switchToBar: "切换为柱状图",
        exportExcel: "导出Excel",
        totalDuration: "总时长",
        unit: "单位/分钟",
        yAxisName: "累计时长（分钟）",
        subtext: "当前状态"
      }
    }
  },
  alarmDistribution: {
    deviceNames: {
      bUnwinding: "B放卷",
      aUnwinding: "A放卷",
      bDieCutting: "B模切",
      aDieCutting: "A模切",
      cStacking: "C叠片",
      dStacking: "D叠片",
      gDischarging: "G下料"
    }
  },
  deviceProtect: {
    title: "设备保护",
    organizationList: "组织列表",
    import: "导入",
    selectFile: "选择文件",
    startImport: "开始导入",
    cancel: "取消",
    importSuccess: "导入成功",
    dailyProtect: {
      title: "日常保养",
      organizationList: "组织列表",
      add: "新增",
      import1: "导入",
      export: "导出",
      batchDelete: "批量删除",
      edit: "编辑",
      delete: "删除",
      view: "查看详情",
      close: "关闭",
      save: "保存",
      table: {
        id: "ID",
        time: "时间",
        startDate: "开始日期",
        endDate: "结束日期",
        operation: "操作"
      },
      form: {
        factory: "工厂",
        workshop: "车间",
        workline: "生产线",
        deck: "机台",
        machineCode: "机台编码",
        station: "工位",
        workstationCode: "工位编码",
        maintenanceOrg: "维护/点检机构",
        maintenanceItem: "维护/点检项",
        maintenanceStandard: "维护/点检标准",
        maintenanceMethod: "维护/点检方法",
        maintenanceFrequency: "维护/点检频率",
        maintenanceStatus: "维护/点检状态",
        maintenancePerson: "维护/点检人",
        maintenanceTime: "维护/点检时间",
        remarks: "备注",
        selectMachine: "请选择机台",
        selectMachineCode: "请选择机台编码"
      },
      status: {
        completed: "已完成",
        uncompleted: "未完成",
        inProgress: "进行中"
      },
      importData: {
        title: "导入数据",
        selectFile: "选择文件",
        startImport: "开始导入",
        cancel: "取消",
        success: "导入成功"
      },
      validation: {
        required: "请填写必填项",
        factory: "请填写工厂",
        workshop: "请填写车间",
        productionLine: "请填写生产线"
      }
    },
    deviceTypes: {
      motor: "电机",
      cylinder: "气缸",
      module: "模组",
      cutter: "切刀"
    },
    deviceInfo: {
      factory: "工厂",
      workshop: "车间",
      productionLine: "线体",
      materialCode: "物料编码",
      materialName: "物料名称",
      specModel: "规格型号",
      brand: "品牌",
      singlePartQty: "单件数量",
      unit: "单位",
      parentMaterialCode: "父物料编码",
      parentMaterialName: "父物料名称",
      workstation: "工作站",
      installTime: "装机时间",
      prodRunCountOrDur: "生产运行次数或时长",
      theoreticalLife: "理论寿命",
      usedLife: "已使用",
      remainingLife: "剩余寿命",
      remarks: "备注",
      noRemarks: "无"
    },
    alertTable: {
      title: "预兆告警",
      time: "时间",
      message: "信息",
      operation: "操作",
      handle: "处理",
      ignore: "忽略",
      status: "处理状态",
      statusTypes: {
        pending: "待处理",
        processing: "处理中",
        completed: "已完成"
      }
    },
    maybeLife: {
      title: "设备寿命预警",
      organizationList: "组织列表",
      add: "新增",
      import: "导入",
      export: "导出",
      batchDelete: "批量删除",
      edit: "编辑",
      delete: "删除",
      view: "查看详情",
      close: "关闭",
      save: "保存",
      form: {
        time: "时间",
        number: "序号",
        factory: "工厂",
        workshop: "车间",
        workline: "线体",
        deck: "机台",
        station: "工位",
        component: "组件",
        workstationCategory: "工位分类",
        materialCode: "物料编码",
        materialName: "物料名称",
        materialSpecification: "物料规格",
        brand: "品牌",
        theoreticalLife: "理论寿命",
        experienceLife: "经验寿命",
        usedLife: "已使用寿命",
        lifeUnit: "寿命单位",
        lifeWarningLowerLimit: "寿命预警下限值",
        lifeWarningUpperLimit: "寿命预警上限值",
        warningTime: "预警时间",
        warningStatus: "预警状态",
        processingStatus: "处理状态",
        processor: "处理人",
        processingTime: "处理时间",
        processingDuration: "处理时长",
        remarks: "备注",
        operation: "操作",
        selectMachine: "请选择机台"
      },
      status: {
        normal: "正常",
        warning: "预警",
        warningInProgress: "预警中",
        danger: "危险",
        unprocessed: "未处理",
        processing: "处理中",
        processed: "已处理"
      },
      importData: {
        title: "导入数据",
        selectFile: "选择文件",
        startImport: "开始导入",
        cancel: "取消",
        success: "导入成功"
      },
      validation: {
        required: "请填写必填项",
        processingTime: "请选择处理时间",
        theoreticalLife: "请填写理论寿命",
        experienceLife: "请填写经验寿命",
        usedLife: "请填写已使用寿命",
        lifeWarningLowerLimit: "请填写寿命预警下限值",
        lifeWarningUpperLimit: "请填写寿命预警上限值",
        warningTime: "请选择预警时间"
      },
      message: {
        deleteSuccess: "删除成功",
        deleteFailed: "删除失败",
        deleteError: "删除过程中出现错误",
        batchDeleteSuccess: "批量删除成功",
        exportSuccess: "导出成功",
        exportError: "导出失败"
      }
    }
  },
  energyConsumption: {
    current: {
      title: "实时能耗",
      exportTable: "表格导出",
      exportImage: "图片导出",
      exportSuccess: "图片批量导出成功",
      exportError: "图片批量导出失败，请查看错误提示",
      electricity: {
        title: "电能统计",
        realTimePower: "实时消耗电能",
        totalPower: "总消耗电能",
        powerConsumption: "能耗功率",
        energyConsumption: "消耗能耗",
        unit: "kWh",
        switchToBar: "切换为柱状图",
        switchToLine: "切换为折线图",
        exportExcel: "导出Excel"
      },
      gas: {
        title: "气能统计",
        realTimeFlow: "实时消耗气能",
        totalFlow: "总消耗气能",
        realTimeGas: "实时气能",
        averageFlow: "平均流量",
        totalConsumption: "累计消耗量",
        unit: "m³",
        switchToBar: "切换为柱状图",
        switchToLine: "切换为折线图",
        exportExcel: "导出Excel"
      }
    }
  },
  rawMaterialWarning: {
    title: "本月原材料预警日历",
    organizationList: "组织列表",
    add: "新增",
    import1: "导入",
    export: "导出",
    batchDelete: "批量删除",
    edit: "编辑",
    delete: "删除",
    view: "查看详情",
    close: "关闭",
    save: "保存",
    operation: "操作",
    attritionStatistics: {
      title: "本月原材料用量日历"
    },
    form: {
      factory: "工厂",
      workshop: "车间",
      productionLine: "产线",
      workline: "线体",
      machineCode: "机台编码",
      deck: "机台名称",
      stationCode: "工位编码",
      station: "工位名称",
      materialType: "原材料类型",
      batch: "原材料批次",
      materialCode: "原材料编码",
      materialName: "原材料名称",
      unit: "原材料单位",
      totalQuantity: "原材料总数量",
      usedQuantity: "原材料用量",
      remainingQuantity: "原材料余量",
      isReplaced: "是否更换原材料",
      feedingTime: "上料时间",
      operator: "操作员姓名",
      operationTime: "操作时间"
    },
    status: {
      yes: "是",
      no: "否"
    },
    importData: {
      title: "导入数据",
      selectFile: "选择文件",
      startImport: "开始导入",
      cancel: "取消",
      success: "导入成功"
    },
    validation: {
      required: "请填写必填项",
      factory: "请填写工厂",
      workshop: "请填写车间",
      productionLine: "请填写线体",
      machineCode: "请填写机台编码",
      machineName: "请填写机台名称",
      stationCode: "请填写工位编码",
      stationName: "请填写工位名称",
      materialType: "请填写原材料类型",
      batch: "请填写原材料批次",
      materialCode: "请填写原材料编码",
      materialName: "请填写原材料名称",
      unit: "请填写原材料单位"
    },
    message: {
      addSuccess: "新增成功",
      editSuccess: "编辑成功",
      deleteSuccess: "删除成功",
      batchDeleteSuccess: "批量删除成功",
      importSuccess: "导入成功",
      exportSuccess: "导出成功",
      operationFailed: "操作失败",
      networkError: "网络请求失败，请重试",
      selectDelete: "请选择要删除的记录",
      requiredFields: "工厂、车间、线体、机台编码、机台名称、工位编码、工位名称、原材料类型、原材料批次、原材料编码、原材料名称、原材料单位为必填项"
    },
    rules: {
      factory: "请填写工厂",
      workshop: "请填写车间",
      productionLine: "请填写线体",
      machineCode: "请填写机台编码",
      machineName: "请填写机台名称",
      stationCode: "请填写工位编码",
      stationName: "请填写工位名称",
      materialType: "请填写原材料类型",
      batch: "请填写原材料批次",
      materialCode: "请填写原材料编码",
      materialName: "请填写原材料名称",
      unit: "请填写原材料单位"
    }
  },
  user: {
    title: "用户列表",
    organizationList: "组织列表",
    import: "导入",
    export: "导出",
    selectFile: "选择文件",
    startImport: "开始导入",
    cancel: "取消",
    sure: "确定",
    importSuccess: "导入成功",
    form: {
      nameOrAccount: "姓名或账号",
      avatar: "头像",
      account: "账号",
      name: "姓名",
      gender: "性别",
      phone: "手机号",
      organization: "组织",
      position: "职位",
      createTime: "创建时间",
      operation: "操作",
      editor: "编辑",
      delete: "删除",
      user: "用户",
      mores: "更多",
      basic: {
        basicInformation: "基础信息",
        nameOrAccount: "姓名或账号",
        avatar: "头像",
        account: "账号",
        name: "姓名",
        gender: "性别",
        phone: "手机号",
        organization: "组织",
        position: "职位",
        createTime: "创建时间",
        operation: "操作",
        nickname: "昵称",
        email: "邮箱",
        birthday: "出生日期",
        entryDate: "入职日期",
        empNo: "员工编号",
        positionLevel: "职级",
        orgAndPosIdList: "部门和职位",
        directorId: "主管",
        choose: "选择",
        selector: {
          title: "选择",
          orgTab: "组织",
          positionTab: "职位",
          roleTab: "角色",
          list: "列表",
          positionTree: "职位列表",
          roleTree: "角色列表",
          addCurrent: "添加当前",
          addSelected: "添加选中",
          delCurrent: "删除当前",
          delSelected: "删除选中",
          selectedCount: "已选择",
          maxCount: "最多选择",
          personUnit: "人",
          addButton: "添加",
          delButton: "删除",
          cancelButton: "取消",
          confirmButton: "确定"
        }
      },
      more: {
        moreInformation: "更多信息",
        nation: "民族",
        nativePlace: "籍贯",
        homeAddress: "家庭住址",
        mailingAddress: "通信地址",
        idCardType: "证件类型",
        idCardNumber: "证件号码",
        cultureLevel: "文化程度",
        politicalOutlook: "政治面貌",
        college: "毕业学校",
        education: "学历",
        eduLength: "学制",
        degree: "学位",
        homeTel: "家庭电话",
        officeTel: "办公电话",
        emergencyContact: "紧急联系人",
        emergencyPhone: "紧急联系电话",
        emergencyAddress: "紧急联系人地址"
      }
    },
    more: {
      resetPassword: "重置密码",
      grantRole: "授权角色",
      grantResource: "授权资源",
      grantPermission: "授权权限"
    },
    resource: {
      NonOvermanaged: "非超管角色不可被授权系统模块菜单资源",
      pleaseSelect: "请选择数据范围",
      parentName: "一级目录",
      title: "菜单",
      button: "按钮授权"
    },
    Permission: {
      note: "注：此功能界面需要与代码查询条件配合使用，并非所有接口都需设置数据范围，多用于业务模块！",
      api: "接口",
      dataScope: "数据范围",
      interfaceName: "请输入接口名称"
    },
    grantRole: {
      title: "全局",
      name: "角色名",
      operation: "操作",
      // orgTab: "组织",
      // positionTab: "职位",
      roleTab: "角色选择",
      organizationList: "组织列表",
      // positionTree: "职位列表",
      // roleTree: "角色列表",
      addCurrent: "添加当前",
      addSelected: "添加选中",
      delCurrent: "删除当前",
      delSelected: "删除选中",
      selectedCount: "已选择",
      maxCount: "最多选择",
      personUnit: "人",
      addButton: "添加",
      delButton: "删除",
      cancelButton: "取消",
      confirmButton: "确定"
    }
  },
  calendar: {
    weekdays: ["一", "二", "三", "四", "五", "六", "日"],
    tooltip: {
      title: "维保详情",
      count: "数量",
      percentage: "占比"
    }
  },
  alarmRealtime: {
    title: "告警实时分析",
    exportTable: "表格导出",
    exportImage: "图片导出",
    exportData: "导出数据",
    time1: "时间",
    time: {
      title: "故障次数",
      machineName: "机台名称",
      faultCount: "故障次数",
      switchToBar: "切换为柱状图",
      switchToLine: "切换为折线图",
      exportExcel: "导出Excel",
      exportFileName: "整线故障时长"
    },
    long: {
      title: "故障时长",
      machineName: "机台名称",
      duration: "故障持续时间",
      durationMinutes: "故障持续时间(分钟)",
      switchToBar: "切换为柱状图",
      switchToLine: "切换为折线图",
      exportExcel: "导出Excel",
      exportFileName: "实时故障时长"
    },
    alarmRate: {
      title: "故障率",
      machineName: "机台名称",
      rate: "故障率",
      ratePercentage: "故障率 (%)",
      switchToBar: "切换为柱状图",
      switchToLine: "切换为折线图",
      exportExcel: "导出Excel",
      exportFileName: "整线故障率"
    },
    machine: "机台",
    currentAlarm: {
      title: "当前告警",
      factory: "工厂名称",
      workshop: "车间名称",
      workline: "线体名称",
      deck: "机台名称",
      station: "工位名称",
      faultType: "故障类型",
      faultName: "故障名称",
      alarmInfo: "告警信息",
      startTime: "故障开始时间",
      duration: "持续时间(分钟)",
      handler: "处理人",
      status: "处理状态"
    },
    charts: {
      time: "故障次数",
      long: "故障持续时间",
      alarmRate: "故障率",
      mttr: "平均修复时间",
      currentAlarm: "当前告警"
    },
    export: {
      success: "图片批量导出成功",
      error: "图片批量导出失败，请查看错误提示"
    },
    mttr: {
      title: "MTTR和MTBF",
      machineName: "机台名称",
      mttr: "MTTR",
      mtbf: "MTBF",
      timeHours: "时间 (小时)",
      hours: "小时",
      switchToBar: "切换为柱状图",
      switchToLine: "切换为折线图",
      exportExcel: "导出Excel",
      exportFileName: "整线Mttr&Mtbf"
    }
  },
  deviceStatus: {
    running: "运行中",
    waiting: "待料中",
    stopped: "停止中",
    fault: "故障中",
    maintenance: "维护中",
    unknownStatus: "未知状态",
    minutes: "分钟",
    windSpeed: "风速",
    metersPerSecond: "米/秒",
    concentration: "浓度",
    ppm: "ppm",
    needsMaintenance: "需要维护",
    maintenanceInProgress: "王工在维修中",
    deviceAging: "器件老化"
  },
  sheetCount: {
    station: "工位名称",
    alarmName: "告警名称",
    alarmInfo: "告警信息",
    startTime: "开始时间",
    duration: "持续时间",
    faultCountTop10: "故障次数 Top10",
    faultCount: "故障次数",
    faultTypeTop10: "故障类型 Top10",
    faultType: "故障类型",
    count: "数量",
    faultTypeCount: "故障类型数量",
    faultRate: "故障率",
    faultDurationStats: "机台故障时长统计"
  },
  alarmHistory: {
    title: "本月故障日历",
    organizationList: "组织列表",
    export: "导出",
    view: "查看",
    close: "关闭",
    save: "保存",
    form: {
      factory: "工厂",
      workshop: "车间",
      workline: "线体",
      deck: "机台",
      station: "工位",
      faultType: "故障类型",
      faultName: "故障名称",
      alarmInfo: "告警信息",
      startTime: "故障开始时间",
      durationMinutes: "故障持续时间（分钟）"
    },
    dialog: {
      add: "新增故障记录",
      edit: "编辑故障记录",
      view: "故障记录详情"
    },
    table: {
      id: "ID",
      time: "时间",
      factory: "工厂",
      workshop: "车间",
      workline: "线体",
      deck: "机台",
      station: "工位",
      faultType: "故障类型",
      faultName: "故障名称",
      alarmInfo: "告警信息",
      startTime: "故障开始时间",
      durationMinutes: "故障持续时间（分钟）",
      operation: "操作"
    },
    exportData: {
      success: "导出成功",
      error: "导出失败"
    }
  },
  treeFilter: {
    text: "请输入关键字进行过滤",
    expandAll: "展开全部",
    collapseAll: "折叠全部",
    all: "全部"
  }
};
