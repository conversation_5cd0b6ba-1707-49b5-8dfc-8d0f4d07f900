<template>
  <ProChart
    :options="chartOptions"
    :fetch-api="fetchData"
    :tool-buttons="['refresh', 'download', 'table', 'compare']"
    @data-loaded="handleDataLoaded"
    :compare-list="compareList"
    default-compare-id="ProcessedCount"
    :machines="machines"
    :init-params="initParams"
    ref="userTable"
    @export-data="handleExportData"
  />
</template>

<script setup lang="tsx">
import moment from "moment";
import { alramAnalysisApi } from "@/api";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
const initParams = ref({
  time: [moment().startOf("day").format("YYYY-MM-DD"), moment().endOf("day").format("YYYY-MM-DD")],
  type: 42
});
const machines = ref<any[]>([]);
const root = document.documentElement;
const chartColor = getComputedStyle(root).getPropertyValue("--el-text-color-regular");
// 颜色配置常量
const COLORS = {
  ProcessedCount: "#00bfff",
  OverdueCount: "#ff4d4f",
  // AlmostDueCount: "#fac858",
  DailyMaintenanceCount: "#2fc25b",
  font: chartColor,
  splitLine: "#eee"
};

const compareList = [
  {
    label: t("common.compareList.ProcessedCount"), //"已处理",
    value: "ProcessedCount"
  },
  {
    label: t("common.compareList.OverdueCount"), //"超时未处理",
    value: "OverdueCount"
  },
  // {
  //   label: "即将到期",
  //   value: "AlmostDueCount"
  // },
  {
    label: t("common.compareList.DailyMaintenanceCount"), //"每日需维护",
    value: "DailyMaintenanceCount"
  }
];

// 图表配置
const chartOptions = ref({
  title: {
    // text: "维护保养",
    // subtext: "总生产量：0",
    // subtext: "维护保养",
    // left: "center",
    left: "6%",
    textStyle: {
      fontSize: 16,
      color: COLORS.font
    },
    subtextStyle: {
      color: COLORS.font
    },
    top: -2
  },
  toolbox: {
    show: true,
    feature: {
      magicType: {
        type: ["line", "bar"],
        title: {
          line: t("common.compareList.switchToLine"), // 切换为折线图
          bar: t("common.compareList.switchToBar") // 切换为柱状图
        }
      },
      saveAsImage: { show: true }
    }
  },
  tooltip: {
    trigger: "axis",
    axisPointer: { type: "shadow" },
    formatter: (params: any) => {
      const ProcessedCount = params.find((p: any) => p.seriesName === t("common.compareList.ProcessedCount"));
      const OverdueCount = params.find((p: any) => p.seriesName === t("common.compareList.OverdueCount"));
      // const AlmostDueCount = params.find((p: any) => p.seriesName === "即将到期");
      const DailyMaintenanceCount = params.find((p: any) => p.seriesName === t("common.compareList.DailyMaintenanceCount"));
      return `
        <div style="padding:5px;min-width:120px">
          <div>
            <span style="display:inline-block;width:10px;height:10px;background:${COLORS.ProcessedCount};border-radius:50%"></span>
            ${t("common.compareList.ProcessedCount")}: ${ProcessedCount?.data}
          </div>
          <div>
            <span style="display:inline-block;width:10px;height:10px;background:${COLORS.OverdueCount};border-radius:50%"></span>
            ${t("common.compareList.OverdueCount")}: ${OverdueCount?.data}
          </div>
          <div>
            <span style="display:inline-block;width:10px;height:10px;background:${COLORS.DailyMaintenanceCount};border-radius:50%"></span>
            ${t("common.compareList.DailyMaintenanceCount")}: ${DailyMaintenanceCount?.data}
          </div>
        </div>
      `;
    }
  },
  legend: {
    data: [t("common.compareList.ProcessedCount"), t("common.compareList.OverdueCount"), t("common.compareList.DailyMaintenanceCount")],
    top: 13,
    textStyle: { color: COLORS.font }
  },
  xAxis: {
    type: "category",
    data: [],
    name: t("common.compareList.time"), //"时间",
    axisLabel: {
      color: COLORS.font,
      interval: 0, // 强制显示所有标签
      rotate: 45 // 旋转标签以避免重叠
    }
  },
  yAxis: [
    {
      type: "value",
      name: t("common.compareList.count"), //"数量",
      axisLabel: { color: COLORS.font },
      splitLine: { lineStyle: { color: COLORS.splitLine } }
    }
    // {
    //   type: "value",
    //   name: "良率 (%)",
    //   min: 0,
    //   max: 100,
    //   axisLabel: {
    //     color: COLORS.rate,
    //     formatter: (value: number) => `${value}%`
    //   },
    //   position: "right"
    // }
  ],
  grid: {
    left: "3%",
    right: "3%",
    bottom: "3%",
    // top: 100,
    containLabel: true
  },
  series: []
});

function transformData(responseData, timeType) {
  const machineMap = new Map();
  const timeSet = new Set();
  const machineIdSet = new Set();

  // 根据时间类型确定时间单位和格式
  let formatPattern;
  switch (timeType) {
    case "Hour":
      formatPattern = "HH:00";
      break;
    case "Month":
      formatPattern = "MM-DD";
      break;
    case "Year":
      formatPattern = "YYYY-MM";
      break;
    default:
      formatPattern = "YYYY";
  }

  // 第一次遍历：聚合数据
  responseData.forEach(item => {
    // if (item[0].Control === "能耗") {
    const machine = item.machine;
    const timeKey = moment(item.start_time).format(formatPattern);
    // console.log(timeKey, "111");

    if (!machineMap.has(machine)) {
      machineMap.set(machine, {
        machine,
        timeBuckets: new Map(), // 存储时间段聚合数据
        ProcessedCount: 0,
        OverdueCount: 0,
        // AlmostDueCount: 0,
        DailyMaintenanceCount: 0
      });
    }

    const machineData = machineMap.get(machine);
    const bucket = machineData.timeBuckets.get(timeKey) || {
      ProcessedCount: 0,
      OverdueCount: 0,
      // AlmostDueCount: 0,
      DailyMaintenanceCount: 0
    };

    // 累加数据到时间段
    bucket.ProcessedCount += item.ProcessedCount;
    bucket.OverdueCount += item.OverdueCount;
    // bucket.AlmostDueCount += item.AlmostDueCount;
    bucket.DailyMaintenanceCount += item.DailyMaintenanceCount;
    machineData.timeBuckets.set(timeKey, bucket);

    // 更新总统计
    machineData.ProcessedCount += item.ProcessedCount;
    machineData.OverdueCount += item.OverdueCount;
    // machineData.AlmostDueCount += item.AlmostDueCount;
    machineData.DailyMaintenanceCount += item.DailyMaintenanceCount;

    // 记录存在的时间点和机台
    timeSet.add(timeKey);
    machineIdSet.add(machine);
    // }
  });

  // 第二次遍历：生成排序后的时间序列
  const categories = Array.from(timeSet).sort((a, b) => moment(a, formatPattern) - moment(b, formatPattern));

  machineMap.forEach(machineData => {
    // 确保所有时间点都有数据（没有的补0）
    const sortedData = categories.map(timeKey => {
      const bucket = machineData.timeBuckets.get(timeKey) || {
        ProcessedCount: 0,
        OverdueCount: 0,
        // AlmostDueCount: 0,
        DailyMaintenanceCount: 0
      };

      return {
        ProcessedCount: bucket.ProcessedCount,
        OverdueCount: bucket.OverdueCount,
        // AlmostDueCount: bucket.AlmostDueCount,
        DailyMaintenanceCount: bucket.DailyMaintenanceCount
      };
    });

    // 转换为需要的数组格式
    machineData.ProcessedCount = sortedData.map(d => d.ProcessedCount);
    machineData.OverdueCount = sortedData.map(d => d.OverdueCount);
    // machineData.AlmostDueCount = sortedData.map(d => d.AlmostDueCount);
    machineData.DailyMaintenanceCount = sortedData.map(d => d.DailyMaintenanceCount);
  });

  // 构建最终数据结构
  const allmachine = Array.from(machineMap.values());
  const compare = [
    {
      ProcessedCount: allmachine.map(m => ({
        machine: m.machine,
        data: m.ProcessedCount,
        total: m.ProcessedCount
      })),
      OverdueCount: allmachine.map(m => ({
        machine: m.machine,
        data: m.OverdueCount,
        total: m.OverdueCount
      })),
      // AlmostDueCount: allmachine.map(m => ({
      //   machine: m.machine,
      //   data: m.AlmostDueCount,
      //   total: m.AlmostDueCount
      // })),
      DailyMaintenanceCount: allmachine.map(m => ({
        machine: m.machine,
        data: m.DailyMaintenanceCount,
        total: m.DailyMaintenanceCount
      }))
    }
  ];

  return {
    allmachine,
    compare,
    categories,
    machines: Array.from(machineIdSet).map(id => ({ id, name: id }))
  };
}
// 修改数据获取函数
const fetchData = async (params: any) => {
  const time = {
    StartDate: moment(params.time[0]).format("YYYY-MM-DD HH:mm:ss").toString(),
    EndDate: moment(params.time[1]).set({ hour: 23, minute: 59, second: 59 }).format("YYYY-MM-DD HH:mm:ss").toString()
  };
  const query = {
    ...time,
    // type: params.type
    Type: 42
  };
  const { data } = await alramAnalysisApi.getListMesReportData(query);

  // 从数据中获取 mode 类型
  let mode = "Hour"; // 默认值
  if (data && data.list.length > 0) {
    mode = data.list[0].type;
  }

  const data1 = transformData(data.list, mode);

  machines.value = data1.machines;

  // 普通模式数据请求
  if (!params.compareMode) {
    const machine = params.machine;
    const machineInfo = data1.allmachine.find(item => item.machine === machine) || data1.allmachine[0];
    if (!machineInfo) {
      console.error(`未找到机台 ${machine} 的数据`);
      return {
        data: {
          categories: ["8:00", "9:00", "10:00", "11:00", "12:00", "13:00", "14:00", "15:00", "16:00", "17:00", "18:00"],
          seriesData: [[], [], []],
          isCompare: false
        }
      };
    }
    const { ProcessedCount, OverdueCount, DailyMaintenanceCount } = machineInfo;
    return {
      data: {
        categories: data1.categories,
        seriesData: [ProcessedCount, OverdueCount, DailyMaintenanceCount],
        isCompare: false
      }
    };
  }

  // 对比模式数据请求
  return {
    data: {
      isCompare: true,
      categories: data1.categories,
      compare: data1.compare
    }
  };
};
// 修改数据加载回调
const handleDataLoaded = (data: any) => {
  // 普通模式数据处理
  if (!data.isCompare) {
    const [ProcessedCount, OverdueCount, DailyMaintenanceCount] = data.seriesData;

    chartOptions.value = {
      ...chartOptions.value,
      title: {
        ...chartOptions.value.title,
        text: t("common.compareList.maintenance1"), //"维护保养",
        left: "6%"
      },
      xAxis: {
        ...chartOptions.value.xAxis,
        data: data.categories
      },
      label: {
        show: true,
        position: "top",
        formatter: "{c}"
      },
      series: [
        { name: t("common.compareList.ProcessedCount"), type: "bar", data: ProcessedCount, itemStyle: { color: COLORS.ProcessedCount } },
        { name: t("common.compareList.OverdueCount"), type: "bar", data: OverdueCount, itemStyle: { color: COLORS.OverdueCount } },
        // { name: "即将到期", type: "bar", data: AlmostDueCount, itemStyle: { color: COLORS.AlmostDueCount } },
        {
          name: t("common.compareList.DailyMaintenanceCount"),
          type: "bar",
          data: DailyMaintenanceCount,
          itemStyle: { color: COLORS.DailyMaintenanceCount }
        }
      ]
    };
  }
};
const emits = defineEmits(["data-ready", "export-data"]);

const handleExportData = (csvContent: string) => {
  // 父组件可以在这里做一些处理，然后继续传递给爷组件

  // eslint-disable-next-line vue/custom-event-name-casing
  emits("export-data", {
    data: csvContent
  });
};
const userTable = ref<any>(null);

defineExpose({
  tableRef: userTable
});
</script>
