<template>
  <ProChart
    :options="chartOptions"
    :fetch-api="fetchData"
    :tool-buttons="['refresh', 'download', 'table', 'compare']"
    @data-loaded="handleDataLoaded"
    :compare-list="compareList"
    default-compare-id="production"
    :machines="machines"
    :init-params="initParams"
    ref="userTable"
    @export-data="handleExportData"
  />
</template>

<script setup lang="tsx">
import moment from "moment";
import { productionReportCapacityApi } from "@/api";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const initParams = ref({
  time: [moment().startOf("day").format("YYYY-MM-DD"), moment().endOf("day").format("YYYY-MM-DD")],
  type: 1
});
const machines = ref<any[]>([]);
const root = document.documentElement;
const chartColor = getComputedStyle(root).getPropertyValue("--el-text-color-regular");
// 颜色配置常量
const COLORS = {
  production: "#00bfff",
  defective: "#ff4d4f",
  rate: "#fac858",
  font: chartColor,
  splitLine: "#eee"
};

const compareList = [
  {
    label: t("productionReport.capacity.compareList.production"),
    value: "production"
  },
  {
    label: t("productionReport.capacity.compareList.defective"),
    value: "defective"
  },
  {
    label: t("productionReport.capacity.compareList.rate"),
    value: "rate"
  }
];

// 图表配置
const chartOptions = ref({
  title: [
    // 主标题 - 产能
    {
      text: t("menu.capacity"),
      left: "6%",
      top: 0,
      textStyle: {
        fontSize: 16,
        fontWeight: "bold",
        color: COLORS.font
      }
    },
    // 副标题 - 总生产量
    {
      subtext: t("productionReport.capacity.totalProduction") + ": 0", // 总生产量：0
      left: "center",
      top: -10, // 适当调整位置使其在主标题下方
      textStyle: {
        fontSize: 16,
        color: COLORS.font
      },
      subtextStyle: {
        color: COLORS.font
      }
    }
  ],
  // title: {
  //   // text: "生产统计看板",
  //   subtext: t("productionReport.capacity.totalProduction") + ": 0", // 总生产量：0
  //   left: "center",
  //   textStyle: {
  //     fontSize: 16,
  //     color: COLORS.font
  //   },
  //   subtextStyle: {
  //     color: COLORS.font
  //   },
  //   top: -12
  // },
  toolbox: {
    show: true,
    feature: {
      magicType: {
        type: ["line", "bar"],
        title: {
          line: t("productionReport.capacity.switchToLine"), // 切换为折线图
          bar: t("productionReport.capacity.switchToBar") // 切换为柱状图
        }
      },
      saveAsImage: { show: true }
    }
  },
  tooltip: {
    trigger: "axis",
    axisPointer: { type: "shadow" },
    formatter: (params: any) => {
      const production = params.find((p: any) => p.seriesName === t("productionReport.capacity.compareList.production"));
      const defective = params.find((p: any) => p.seriesName === t("productionReport.capacity.compareList.defective"));
      const rate = params.find((p: any) => p.seriesName === t("productionReport.capacity.compareList.rate"));
      return `
        <div style="padding:5px;min-width:120px">
          <div>
            <span style="display:inline-block;width:10px;height:10px;background:${COLORS.production};border-radius:50%"></span>
             ${t("productionReport.capacity.compareList.production")}: ${production?.data}
          </div>
          <div>
            <span style="display:inline-block;width:10px;height:10px;background:${COLORS.defective};border-radius:50%"></span>
            ${t("productionReport.capacity.compareList.defective")}: ${defective?.data}
          </div>
          <div>
            <span style="display:inline-block;width:10px;height:10px;background:${COLORS.rate};border-radius:50%"></span>
            ${t("productionReport.capacity.compareList.rate")}: ${rate?.data}%
          </div>
        </div>
      `;
    }
  },
  legend: {
    data: [
      t("productionReport.capacity.compareList.production"), // 生产计数
      t("productionReport.capacity.compareList.defective"), // 不良计数
      t("productionReport.capacity.compareList.rate") // 良率
    ],
    top: 13,
    textStyle: { color: COLORS.font }
  },
  xAxis: {
    type: "category",
    data: [],
    name: t("productionReport.capacity.time"),
    axisLabel: {
      color: COLORS.font,
      interval: 0, // 强制显示所有标签
      rotate: 45 // 旋转标签以避免重叠
    }
  },
  yAxis: [
    {
      type: "value",
      name: t("productionReport.capacity.productionQuantity"), // 生产数量
      axisLabel: { color: COLORS.font },
      splitLine: { lineStyle: { color: COLORS.splitLine } }
    },
    {
      type: "value",
      name: t("productionReport.capacity.yieldRateUnit"), // 良率 (%)
      min: 0,
      max: 100,
      axisLabel: {
        color: COLORS.rate,
        formatter: (value: number) => `${value}%`
      },
      position: "right"
    }
  ],
  grid: {
    left: "3%",
    right: "3%",
    bottom: "3%",
    // top: 100,
    containLabel: true
  },
  series: []
});

function transformData(responseData, timeType) {
  const machineMap = new Map();
  const timeSet = new Set();
  const machineIdSet = new Set();

  // 根据时间类型确定时间单位和格式
  let formatPattern;
  switch (timeType) {
    case "Hour":
      formatPattern = "HH:00";
      break;
    case "Mon":
      formatPattern = "MM-DD";
      break;
    case "Year":
      formatPattern = "YYYY-MM";
      break;
    default:
      formatPattern = "YYYY";
  }

  // 第一次遍历：聚合数据
  responseData.forEach(item => {
    const machine = item.deck;
    const timeKey = moment(item.starttime).format(formatPattern);

    if (!machineMap.has(machine)) {
      machineMap.set(machine, {
        machine,
        timeBuckets: new Map(), // 存储时间段聚合数据
        totalProduction: 0,
        totalDefective: 0
      });
    }

    const machineData = machineMap.get(machine);
    const bucket = machineData.timeBuckets.get(timeKey) || {
      production: 0,
      defective: 0
    };

    // 累加数据到时间段
    bucket.production += item.prodCount;
    bucket.defective += item.badCount;
    machineData.timeBuckets.set(timeKey, bucket);

    // 更新总统计
    machineData.totalProduction += item.prodCount;
    machineData.totalDefective += item.badCount;

    // 记录存在的时间点和机台
    timeSet.add(timeKey);
    machineIdSet.add(machine);
  });

  // 第二次遍历：生成排序后的时间序列
  const categories = Array.from(timeSet).sort((a, b) => moment(a, formatPattern) - moment(b, formatPattern));

  machineMap.forEach(machineData => {
    // 确保所有时间点都有数据（没有的补0）
    const sortedData = categories.map(timeKey => {
      const bucket = machineData.timeBuckets.get(timeKey) || {
        production: 0,
        defective: 0
      };

      return {
        production: bucket.production,
        defective: bucket.defective,
        rate: bucket.production > 0 ? (((bucket.production - bucket.defective) / bucket.production) * 100).toFixed(1) : 0
      };
    });

    // 转换为需要的数组格式
    machineData.production = sortedData.map(d => d.production);
    machineData.defective = sortedData.map(d => d.defective);
    machineData.rate = sortedData.map(d => parseFloat(d.rate));

    // 计算总合格率
    machineData.totalRate =
      machineData.totalProduction > 0
        ? (((machineData.totalProduction - machineData.totalDefective) / machineData.totalProduction) * 100).toFixed(1)
        : 0;
  });

  // 构建最终数据结构
  const allmachine = Array.from(machineMap.values());
  const compare = [
    {
      production: allmachine.map(m => ({
        machine: m.machine,
        data: m.production,
        total: m.totalProduction
      })),
      defective: allmachine.map(m => ({
        machine: m.machine,
        data: m.defective,
        total: m.totalDefective
      })),
      rate: allmachine.map(m => ({
        machine: m.machine,
        data: m.rate,
        total: parseFloat(m.totalRate)
      }))
    }
  ];

  return {
    allmachine,
    compare,
    categories,
    machines: Array.from(machineIdSet).map(id => ({ id, name: id }))
  };
}

// 修改数据获取函数
const fetchData = async (params: any) => {
  const time = {
    StartDate: moment(params.time[0]).format("YYYY-MM-DD HH:mm:ss").toString(),
    EndDate: moment(params.time[1]).set({ hour: 23, minute: 59, second: 59 }).format("YYYY-MM-DD HH:mm:ss").toString()
  };
  const query = {
    ...time,
    type: params.type
  };
  const { data } = await productionReportCapacityApi.getListMesReportData(query);

  // 从数据中获取 mode 类型
  let mode = "Hour"; // 默认值
  if (data && data.list.length > 0) {
    mode = data.list[0].type;
  }

  const data1 = transformData(data.list, mode);
  machines.value = data1.machines;

  // 普通模式数据请求
  if (!params.compareMode) {
    const machine = params.machine;
    const machineInfo = data1.allmachine.find(item => item.machine === machine) || data1.allmachine[0];
    if (!machineInfo) {
      console.error(`未找到机台 ${machine} 的数据`);
      return {
        data: {
          categories: ["8:00", "9:00", "10:00", "11:00", "12:00", "13:00", "14:00", "15:00", "16:00", "17:00", "18:00"],
          seriesData: [[], [], []],
          isCompare: false
        }
      };
    }
    const { production, defective, rate } = machineInfo;
    return {
      data: {
        categories: data1.categories,
        seriesData: [production, defective, rate],
        isCompare: false
      }
    };
  }

  // 对比模式数据请求
  return {
    data: {
      isCompare: true,
      categories: data1.categories,
      compare: data1.compare
    }
  };
};

// 修改数据加载回调
const handleDataLoaded = (data: any) => {
  // 普通模式数据处理
  if (!data.isCompare) {
    const [production, defective, rate] = data.seriesData;

    chartOptions.value = {
      ...chartOptions.value,
      title: [
        // 主标题保持不变
        chartOptions.value.title[0],
        // 只更新副标题的内容
        {
          ...chartOptions.value.title[1],
          subtext: `${t("productionReport.capacity.totalProduction")}：${production.reduce((a: number, b: number) => a + b, 0)}`
        }
      ],
      // title: {
      //   ...chartOptions.value.title,
      //   subtext: `${t("productionReport.capacity.totalProduction")}：${production.reduce((a: number, b: number) => a + b, 0)}`
      // },
      xAxis: {
        ...chartOptions.value.xAxis,
        data: data.categories
      },
      label: {
        show: true,
        position: "top",
        formatter: "{c}"
      },
      series: [
        {
          name: t("productionReport.capacity.compareList.production"),
          type: "bar",
          data: production,
          itemStyle: { color: COLORS.production }
        },
        {
          name: t("productionReport.capacity.compareList.defective"),
          type: "bar",
          data: defective,
          itemStyle: { color: COLORS.defective }
        },
        {
          name: t("productionReport.capacity.compareList.rate"),
          type: "line",
          yAxisIndex: 1,
          data: rate,
          itemStyle: { color: COLORS.rate }
        }
      ]
    };
  }
};
const emits = defineEmits(["data-ready", "export-data"]);

const handleExportData = (csvContent: string) => {
  // 父组件可以在这里做一些处理，然后继续传递给爷组件

  // eslint-disable-next-line vue/custom-event-name-casing
  emits("export-data", {
    data: csvContent
  });
};
const userTable = ref<any>(null);

defineExpose({
  tableRef: userTable
});
</script>
